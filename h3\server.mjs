import { H3, serve } from 'h3'

const app = new H3()
app.get('/', event => '⚡️ Tadaa!111')
app.post('/api/getTaskList', (event) => {
  return {
    success: 1,
    data: [
      {
        taskId: '123123',
        taskName: 'task1',
      },
      {
        taskId: '5475235',
        taskName: 'task22222222',
      },
    ],
  }
})
app.post('/api/getTask', (event) => {
  return {
    success: 1,
    taskId: '123123',
    taskName: 'task1',
    data: [
      [
        '电脑1',
        '王五成',
        '部门1',
        '戴尔 ESDV-123112',
        '办公室A001',
        40446546546654,
      ],
      [
        '电脑2',
        '张三中',
        '部门2',
        '戴尔 ESDV-123113',
        '办公室A002',
        40446546546655,
      ],
      [
        '电脑3',
        '张三中',
        '部门3',
        '戴尔 ESDV-123114',
        'Ab - 134',
        40446546546656,
      ],
      [
        '电脑4',
        '张三中',
        '部门4',
        '戴尔 ESDV-123115',
        '办公室A004',
        40446546546657,
      ],
      [
        '电脑5',
        '李四四',
        '部门5',
        '戴尔 ESDV-123116',
        '公共空间',
        40446546546658,
      ],
      [
        '电脑6',
        '王五成',
        '部门6',
        '戴尔 ESDV-123117',
        '办公室A006',
        40446546546659,
      ],
      [
        '办公桌1（配三抽活动柜、长副柜各一个）',
        '李四四',
        '部门7',
        '美的3匹KFR-72LW/BDN8Y-YA401(1)A',
        'Ab - 134',
        40446546546660,
      ],
      [
        '办公桌2（配三抽活动柜、长副柜各一个）',
        '李四四',
        '部门8',
        '美的3匹KFR-72LW/BDN8Y-YA401(2)A',
        '办公室A008',
        40446546546661,
      ],
      [
        '办公桌3（配三抽活动柜、长副柜各一个）',
        '张三中',
        '部门9',
        '美的3匹KFR-72LW/BDN8Y-YA401(3)A',
        '办公室A009',
        40446546546662,
      ],
      [
        '办公桌4（配三抽活动柜、长副柜各一个）',
        '王五成',
        '部门10',
        '美的3匹KFR-72LW/BDN8Y-YA401(4)A',
        '办公室A001',
        40446546546663,
      ],
      [
        '办公桌5（配三抽活动柜、长副柜各一个）',
        '王五成',
        '部门11',
        '美的3匹KFR-72LW/BDN8Y-YA401(5)A',
        'Ab - 134',
        40446546546664,
      ],
      [
        '办公桌6（配三抽活动柜、长副柜各一个）',
        '王五成',
        '部门12',
        '美的3匹KFR-72LW/BDN8Y-YA401(6)A',
        '公共空间',
        40446546546665,
      ],
      [
        '办公桌7（配三抽活动柜、长副柜各一个）',
        '李四四',
        '部门13',
        '美的3匹KFR-72LW/BDN8Y-YA401(7)A',
        '办公室A004',
        40446546546666,
      ],
      [
        '办公桌8（配三抽活动柜、长副柜各一个）',
        '王五成',
        '部门14',
        '美的3匹KFR-72LW/BDN8Y-YA401(8)A',
        '办公室A008',
        40446546546667,
      ],
      [
        '办公桌9（配三抽活动柜、长副柜各一个）',
        '张三中',
        '部门15',
        '美的3匹KFR-72LW/BDN8Y-YA401(9)A',
        '公共空间',
        40446546546668,
      ],
      [
        '办公桌10（配三抽活动柜、长副柜各一个）',
        '王五成',
        '部门16',
        '美的3匹KFR-72LW/BDN8Y-YA401(10)A',
        '办公室A001',
        40446546546669,
      ],
      [
        '美的空调12412412',
        '孙历史',
        '部门17',
        '美的3匹KFR-72LW/BDN8Y-YA401(11)A',
        'Ab - 134',
        40446546546670,
      ],
      [
        '美的空调12412413',
        '孙历史',
        '部门18',
        '美的3匹KFR-72LW/BDN8Y-YA401(12)A',
        '办公室A006',
        40446546546671,
      ],
      [
        '美的空调12412414',
        '孙历史',
        '部门19',
        '美的3匹KFR-72LW/BDN8Y-YA401(13)A',
        '办公室A007',
        40446546546672,
      ],
      [
        '美的空调12412415',
        '孙历史',
        '部门20',
        '美的3匹KFR-72LW/BDN8Y-YA401(14)A',
        '办公室A006',
        40446546546673,
      ],
      [
        '美的空调12412416',
        '孙历史',
        '部门21',
        '美的3匹KFR-72LW/BDN8Y-YA401(15)A',
        '公共空间',
        40446546546674,
      ],
      [
        '美的空调12412417',
        '孙历史',
        '部门22',
        '美的3匹KFR-72LW/BDN8Y-YA401(16)A',
        '公共空间',
        40446546546675,
      ],
      [
        '美的空调12412418',
        '孙历史',
        '部门23',
        '美的3匹KFR-72LW/BDN8Y-YA401(17)A',
        '公共空间',
        40446546546676,
      ],
      [
        '美的空调12412419',
        '孙历史',
        '部门24',
        '美的3匹KFR-72LW/BDN8Y-YA401(18)A',
        '办公室A006',
        40446546546677,
      ],
      [
        '美的空调12412420',
        '孙历史',
        '部门25',
        '美的3匹KFR-72LW/BDN8Y-YA401(19)A',
        '办公室A007',
        40446546546678,
      ],
    ],
  }
})

app.post('/api/title', async (event) => {
  console.log('title', await event.req.json())
  return {
    success: 1,
  }
})

app.post('/api/table', async (event) => {
  console.log('table', await event.req.json())
  return {
    success: 1,
  }
})

serve(app, {
  // hostname: "*************",
  hostname: '**************',
  port: 8812,
})
