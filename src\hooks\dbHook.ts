import type { TaskDetailRes } from '@/service/api'
import type {
  AddressForm,
  EquipmentForm,
  OptionalEquipmentFormBase,
} from '@/store/dbStore'
import dayjs from 'dayjs'
import { nanoid } from 'nanoid'
import { storeToRefs } from 'pinia'
import { throttle } from 'throttle-debounce'
import { sendDataToServerAPI } from '@/service/api'
import {
  dbStore,
} from '@/store/dbStore'
import { pointerStore } from '@/store/pointerStore'
import { stepStore } from '@/store/stepStore'
import { CreateStep } from '@/utils/enum'
import { delIdWithFindIndex } from '@/utils/tool'

export interface XlsxFileData {
  资产名称: string
  使用人: string
  使用部门: string
  规格型号: string
  存放地点: string
  条形码: string
}

function getDefaultAddressParam() {
  return {
    planCodeArr: [],
    completedCodeArr: [],
    unfinishedCodeArr: [],
    unplannedCodeArr: [],
    completedPercent: 0,
  }
}

export function dbHook() {
  const useDbStore = dbStore()
  const { taskDataDb, addressDataDb, equDataDb, isOnlineMode }
    = storeToRefs(useDbStore)
  const usePointerStore = pointerStore()
  const { activeAddressList, activeEquipmentList }
    = storeToRefs(usePointerStore)
  const useStepStore = stepStore()
  const { createStep, activeTaskId } = storeToRefs(useStepStore)

  /**
   * 组合需要输出的数据
   * @param data
   */
  const combinedData = (data: string[][]) => {
    const isCompletedArr = []
    activeAddressList.value.forEach((item) => {
      isCompletedArr.push(...item.completedCodeArr)
    })

    activeEquipmentList.value.forEach((item) => {
      // 是否已清点
      const isCompleted = isCompletedArr.includes(item.code)
      data.push([
        item.name, // 资产名称
        item.modelType, // 规格型号
        `\`${item.code}`, // 条形码
        item.userName, // 使用人
        item.addressName, // 存放地点
        item.department, // 使用部门
        isCompleted ? '已清点' : '未清点', // 是否清点
        item.hadModify_name ? '变更' : '', // 是否更变资产名
        item.hadModify_modelType ? '变更' : '', // 是否变更规格型号
        item.hadModify_userName ? '变更' : '', // 是否变更使用人
        item.hadModify_address ? '变更' : '', // 是否变更存放地点
        item.hadModify_department ? '变更' : '', // 是否变更使用部门
      ])
    })
  }

  const sendDataToServer = (isShowToast: boolean = false) => {
    if (isOnlineMode.value) {
      // 发送数据到服务器
      // 节流，3秒内只发送一次
      throttle(1000, () => {
        const data: string[][] = []
        combinedData(data)

        sendDataToServerAPI({
          taskId: activeTaskId.value,
          table: data,
        })
          .then((res) => {
            if (isShowToast && !res.data.success) {
              uni.showToast({
                title: '联网导出失败',
                duration: 3500,
              })
            }
          })
          .catch(() => {
            if (isShowToast) {
              uni.showToast({
                title: '联网导出失败',
                duration: 3500,
              })
            }
          })
      })()
    }
  }

  const addTask = (id?: string, name?: string) => {
    if (!id) {
      id = nanoid()
    }
    if (!name) {
      name = `任务${id}`
    }
    taskDataDb.value.push({
      id,
      name,
      createDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      conflictResolvedEquCodeArr: [],
    })
    return id
  }

  const updateTask = (id: string, name: string) => {
    // name查重
    if (taskDataDb.value.some(taskItem => taskItem.name === name)) {
      uni.showToast({
        title: '任务名不能重复',
        duration: 2000,
      })
    }
    else {
      const taskIndex = taskDataDb.value.findIndex(
        taskItem => taskItem.id === id,
      )
      if (taskIndex !== -1) {
        taskDataDb.value[taskIndex].name = name
      }

      sendDataToServer()
    }
  }

  const addAddress = (taskId: string, name: string, equCode: string) => {
    // 先筛选出任务下的所有地址
    // name查重
    const existingAddress = addressDataDb.value.find(
      addressItem =>
        addressItem.taskId === taskId && addressItem.name === name,
    )
    if (existingAddress) {
      uni.showToast({
        title: '地址名不能重复',
        duration: 2000,
      })
      return null
    }
    else {
      const id = nanoid()
      // 添加地址
      const params = {
        id,
        name,
        taskId,
        ...getDefaultAddressParam(),
      }
      params.planCodeArr.push(equCode)
      params.unfinishedCodeArr.push(equCode)
      addressDataDb.value.push(params)
      return id
    }
  }

  const updateEqu = (
    equId: string,
    data: OptionalEquipmentFormBase,
    isChangeAddress: boolean,
  ): boolean => {
    const equIndex = equDataDb.value.findIndex(item => item.id === equId)
    if (equIndex === -1) {
      uni.showToast({
        title: `未找到id:${equId}`,
        duration: 2000,
      })
      return false
    }

    if (isChangeAddress) {
      // 原地址Arr相关----------------------------------------
      // 取消原来所在地址表数据中planCodeArr、completedCodeArr、unfinishedCodeArr 已有的数据，重算百分比
      // 取出原地址指针
      const addressItem = addressDataDb.value.find(
        item => item.id === equDataDb.value[equIndex].addressId,
      )
      // 去掉addressItem.planCodeArr中equCode
      addressItem.planCodeArr = addressItem.planCodeArr.filter(
        item => item !== equDataDb.value[equIndex].code,
      )
      // 原来是否已经完成
      const isCompleted = addressItem.completedCodeArr.includes(
        equDataDb.value[equIndex].code,
      )
      if (isCompleted) {
        // 如果已完成里面有对应的code，清掉
        addressItem.completedCodeArr = addressItem.completedCodeArr.filter(
          item => item !== equDataDb.value[equIndex].code,
        )
      }
      else {
        // 如果已完成里面没有，则未完成里面就会有，清掉未完成里面的
        addressItem.unfinishedCodeArr = addressItem.unfinishedCodeArr.filter(
          item => item !== equDataDb.value[equIndex].code,
        )
      }
      // 无需处理额外发现的，因为这个是原本所属地址，额外发现不可能有这个 code
      // 重算百分比
      if (addressItem.planCodeArr.length === 0) {
        addressItem.completedPercent = 100
      }
      else {
        addressItem.completedPercent = Math.floor(
          (addressItem.completedCodeArr.length
            / addressItem.planCodeArr.length)
          * 100,
        )
      }
      // 新地址Arr相关----------------------------------------------
      // 添加到新地址的planCodeArr、completedCodeArr、unfinishedCodeArr中
      const newAddressItem = addressDataDb.value.find(
        item => item.id === data.addressId,
      )
      // 添加到planCodeArr中的，修改地址的时候添加过了，就无需添加
      if (
        !newAddressItem.planCodeArr.includes(equDataDb.value[equIndex].code)
      ) {
        newAddressItem.planCodeArr.push(equDataDb.value[equIndex].code)
      }
      // 如果本来已完成，添加到新completedCodeArr中
      if (isCompleted) {
        newAddressItem.completedCodeArr.push(equDataDb.value[equIndex].code)
        newAddressItem.unfinishedCodeArr
          = newAddressItem.unfinishedCodeArr.filter(
            item => item !== equDataDb.value[equIndex].code,
          )
      }
      else {
        // 否则添加到新未完成中，如果新建地址的时候，放过就不用放
        if (
          !newAddressItem.unfinishedCodeArr.includes(
            equDataDb.value[equIndex].code,
          )
        ) {
          newAddressItem.unfinishedCodeArr.push(equDataDb.value[equIndex].code)
        }
      }
      // 额外发现的冲突解决------------------------------------------
      // 遍历addressDb中taskId===addressItem.taskId的item.unplannedCodeArr，看是否有equDataDb.value[equIndex].code，有则删掉
      let needConflictResolved = false // 是否需要解决冲突
      addressDataDb.value.forEach((aItem) => {
        if (aItem.taskId === addressItem.taskId) {
          if (aItem.unplannedCodeArr.includes(equDataDb.value[equIndex].code)) {
            needConflictResolved = true
            aItem.unplannedCodeArr = aItem.unplannedCodeArr.filter(
              item => item !== equDataDb.value[equIndex].code,
            )
          }
        }
      })
      // 如果解决了冲突，那么以后不会再出现这个设备的冲突提示，所以要添加到conflictResolvedEquCodeArr中
      if (needConflictResolved) {
        const taskItem = taskDataDb.value.find(
          item => item.id === addressItem.taskId,
        )
        taskItem.conflictResolvedEquCodeArr.push(
          equDataDb.value[equIndex].code,
        )
        // 如果解决了冲突，证明已经扫描出来过，应该放到已完成列表中，去掉未清点的列表
        // 先判断newAddressItem.completedCodeArr是否已经加入
        if (
          !newAddressItem.completedCodeArr.includes(
            equDataDb.value[equIndex].code,
          )
        ) {
          newAddressItem.completedCodeArr.push(equDataDb.value[equIndex].code)
          newAddressItem.unfinishedCodeArr
            = newAddressItem.unfinishedCodeArr.filter(
              item => item !== equDataDb.value[equIndex].code,
            )
        }
      }
      // 重新计算百分比
      if (newAddressItem.planCodeArr.length === 0) {
        newAddressItem.completedPercent = 100
      }
      else {
        newAddressItem.completedPercent = Math.floor(
          (newAddressItem.completedCodeArr.length
            / newAddressItem.planCodeArr.length)
          * 100,
        )
      }
    }
    // 更新设备数据
    equDataDb.value[equIndex] = {
      ...equDataDb.value[equIndex],
      ...data,
    }

    sendDataToServer()

    return true
  }

  const delEqu = (equId: string) => {
    delIdWithFindIndex(equDataDb.value, equId)
    sendDataToServer()
  }

  const delAddress = (addressId: string) => {
    // 删除所有equDataDb.value中item.addressId === addressId的item
    equDataDb.value = equDataDb.value.filter(
      equItem => equItem.addressId !== addressId,
    )
    // 删除addressDataDb.value中id===addressId的唯一元素
    delIdWithFindIndex(addressDataDb.value, addressId)
    sendDataToServer()
  }

  const delTask = (taskId: string) => {
    // 获取需要删除的地址id，用于删除equDataDb.value中对应地址id的元素
    const delEquId = addressDataDb.value
      .filter(addressItem => addressItem.taskId === taskId)
      .map(addressItem => addressItem.id)
    // 删除addressDataDb中item.taskId === taskId的item
    addressDataDb.value = addressDataDb.value.filter(
      addressItem => addressItem.taskId !== taskId,
    )
    // 删除equDataDb中item.addressId === delEquId的item
    equDataDb.value = equDataDb.value.filter(
      equItem => !delEquId.includes(equItem.addressId),
    )
    // 删除taskDataDb中taskId===taskId的item
    delIdWithFindIndex(taskDataDb.value, taskId)
    sendDataToServer()
  }

  /**
   * 判断文件数据是否有重复条形码
   * @param assets
   */
  // 函数重载签名
  function hasDuplicateBarcodes(assets: string[][], isNetWork: true): boolean
  function hasDuplicateBarcodes(
    assets: XlsxFileData[],
    isNetWork: false,
  ): boolean
  // 实现函数
  function hasDuplicateBarcodes(
    assets: string[][] | XlsxFileData[],
    isNetWork: boolean,
  ): boolean {
    const barcodeSet = new Set<string>()

    if (isNetWork) {
      // 联网模式下，条形码不能重复
      for (const asset of assets as string[][]) {
        const barcode = asset[5] // 条形码在第6个位置（索引为5）
        if (barcodeSet.has(barcode)) {
          return true // 发现重复条形码
        }
        barcodeSet.add(barcode)
      }
      return false // 没有发现重复条形码
    }
    else {
      for (const asset of assets as XlsxFileData[]) {
        if (barcodeSet.has(asset['条形码'])) {
          return true // 发现重复条形码
        }
        barcodeSet.add(asset['条形码'])
      }
      return false // 没有发现重复条形码
    }
  }

  /* 将文件数据建库，获取到的格式如下：
      [
        {
            "资产名称": "电脑1",
            "使用人":"王五",
            "使用部门":"部门1",
            "规格型号":"ESDV-123",
            "存放地点":"办公室1",
            "条形码":"155151",
        }
    ]
   */
  const saveLocalFileData = (data: XlsxFileData[]) => {
    // 先判断[条形码]是否有重复
    if (hasDuplicateBarcodes(data, false)) {
      uni.showToast({
        title: '条形码不能重复',
        duration: 2000,
      })
      return
    }
    // 先建立任务
    const taskId = addTask()
    // 新建地址临时容器
    const addressData: AddressForm[] = []
    // 新建设备临时容器
    const equData: EquipmentForm[] = []
    const defaultEquData = {
      hadPrintRfid: false,
      hadModify_modelType: false,
      hadModify_name: false,
      hadModify_userName: false,
      hadModify_address: false,
      hadModify_department: false,
      scanNum: 0,
    }

    data.forEach((equItem) => {
      // 先建立地址库，要看是否已经建立
      const index = addressData.findIndex(
        d => d.name === equItem['存放地点'],
      )
      if (index !== -1) {
        // 地址已存在，直接添加设备
        equData.push({
          id: nanoid(),
          code: equItem['条形码'],
          name: equItem['资产名称'],
          addressId: addressData[index].id,
          addressName: equItem['存放地点'],
          modelType: equItem['规格型号'],
          userName: equItem['使用人'],
          department: equItem['使用部门'],
          ...defaultEquData,
        })
        addressData[index].planCodeArr.push(equItem['条形码'])
        addressData[index].unfinishedCodeArr.push(equItem['条形码'])
        // console.log(addressData[index])
      }
      else {
        // 地址不存在，添加地址
        const addressId = nanoid()
        const params: AddressForm = {
          id: addressId,
          name: equItem['存放地点'],
          taskId,
          ...getDefaultAddressParam(),
        }
        params.planCodeArr.push(equItem['条形码'])
        params.unfinishedCodeArr.push(equItem['条形码'])
        addressData.push(params)
        // console.log('nooooooooooooooooo', addressData[addressData.length - 1])
        // 添加设备
        equData.push({
          id: nanoid(),
          code: equItem['条形码'],
          name: equItem['资产名称'],
          addressId,
          addressName: equItem['存放地点'],
          modelType: equItem['规格型号'],
          userName: equItem['使用人'],
          department: equItem['使用部门'],
          ...defaultEquData,
        })
      }
    })
    addressDataDb.value.push(...addressData)
    equDataDb.value.push(...equData)
  }

  /*   [
    [
      "资产名称0",
      "使用人1",
      "使用部门2",
      "规格型号3",
      "存放地点4",
      "条形码5"
    ]
  ] */
  const saveNetWorkFileData = (data: TaskDetailRes) => {
    // 先判断[条形码]是否有重复
    if (hasDuplicateBarcodes(data.data, true)) {
      uni.showToast({
        title: '条形码不能重复',
        duration: 2000,
      })
      return
    }
    // 先建立任务
    const taskId = addTask(data.taskId, data.taskName)
    // 新建地址临时容器
    const addressData: AddressForm[] = []
    // 新建设备临时容器
    const equData: EquipmentForm[] = []
    const defaultEquData = {
      hadPrintRfid: false,
      hadModify_modelType: false,
      hadModify_name: false,
      hadModify_userName: false,
      hadModify_address: false,
      hadModify_department: false,
      scanNum: 0,
    }

    data.data.forEach((equItem) => {
      const index = addressData.findIndex(d => d.name === equItem[4])
      if (index !== -1) {
        // 地址已存在，直接添加设备
        equData.push({
          id: nanoid(),
          code: equItem[5],
          name: equItem[0],
          addressId: addressData[index].id,
          addressName: equItem[4],
          modelType: equItem[3],
          userName: equItem[1],
          department: equItem[2],
          ...defaultEquData,
        })
        addressData[index].planCodeArr.push(equItem[5])
        addressData[index].unfinishedCodeArr.push(equItem[5])
        // console.log(addressData[index])
      }
      else {
        // 地址不存在，添加地址
        const addressId = nanoid()
        const params: AddressForm = {
          id: addressId,
          name: equItem[4],
          taskId,
          ...getDefaultAddressParam(),
        }
        params.planCodeArr.push(equItem[5])
        params.unfinishedCodeArr.push(equItem[5])
        addressData.push(params)
        // console.log('nooooooooooooooooo', addressData[addressData.length - 1])
        // 添加设备
        equData.push({
          id: nanoid(),
          code: equItem[5],
          name: equItem[0],
          addressId,
          addressName: equItem[4],
          modelType: equItem[3],
          userName: equItem[1],
          department: equItem[2],
          ...defaultEquData,
        })
      }
    })
    addressDataDb.value.push(...addressData)
    equDataDb.value.push(...equData)
  }

  const resetDb = () => {
    taskDataDb.value = []
    addressDataDb.value = []
    equDataDb.value = []
    activeAddressList.value = []
    activeEquipmentList.value = []
    createStep.value = CreateStep.createTask
  }

  watch(activeAddressList, (newVal) => {
    sendDataToServer()
  }, {
    deep: true,
  })

  return {
    taskDataDb,
    addressDataDb,
    equDataDb,
    combinedData,
    sendDataToServer,
    saveLocalFileData,
    saveNetWorkFileData,
    addTask,
    addAddress,
    updateTask,
    updateEqu,
    delTask,
    delEqu,
    delAddress,
    resetDb,
  }
}
