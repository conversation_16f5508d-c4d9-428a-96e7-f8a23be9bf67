<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '按下扳机键开始/结束清点',
  },
}
</route>

<script lang="ts" setup>
import type {
  AddressForm,
  EquipmentForm,
  OptionalEquipmentFormBase,
} from '@/store/dbStore'
import { storeToRefs } from 'pinia'
import { useMessage, useToast } from 'wot-design-uni'
import { dbHook } from '@/hooks/dbHook'
import {
  dbStore,
} from '@/store/dbStore'
import { pointerStore } from '@/store/pointerStore'
import { stepStore } from '@/store/stepStore'
import { getColor, rgbaToHex } from '@/utils/colorUtils'

const rfidModule = uni.requireNativePlugin('rfidModule')
const globalEvent = uni.requireNativePlugin('globalEvent')
const modal = uni.requireNativePlugin('modal')

const message = useMessage()
const toast = useToast()
const useStepStore = stepStore()
const { activeTaskId, activeAddressId } = storeToRefs(useStepStore)
const usePointerStore = pointerStore()
const { activeAddressList, activeEquipmentList } = storeToRefs(usePointerStore)
const useDbStore = dbStore()
const { taskDataDb } = storeToRefs(useDbStore)
const { addressDataDb, equDataDb, updateEqu, addAddress } = dbHook()

enum Tab {
  planCount,
  completedCount,
  unfinishedCount,
  unplannedCount,
}

// 指针数据
const activeAddressItem = ref<AddressForm>({
  id: '',
  name: '',
  taskId: '',
  planCodeArr: [],
  completedCodeArr: [],
  unfinishedCodeArr: [],
  unplannedCodeArr: [],
  completedPercent: 0,
})
const tab = ref<Tab>(Tab.planCount)
// 当前选择显示的设备列表
const showList = ref<EquipmentForm[]>([])

// 消息队列
// const messageQueue: string[] = []
const messageQueue: Set<string> = new Set()
const isShowEditor = ref(false)
const isStartHandleQueue = ref(false)
const isShowPicker = ref(false)

// 性能监控变量
const queueStats = reactive({
  maxQueueSize: 0,
  totalProcessed: 0,
  averageProcessTime: 0,
  lastProcessTime: 0,
})
interface EquForm {
  id: string
  code: string
  name: string
  addressId: string
  addressName: string
  modelType: string
  userName: string
  department: string
}

const form = ref()
const formModel = reactive<EquForm>({
  id: '',
  code: '',
  name: '',
  addressId: '',
  addressName: '',
  modelType: '',
  userName: '',
  department: '',
})
// 用于检测是否有修改
const formModelCopy = {
  name: '',
  addressName: '',
  modelType: '',
  userName: '',
  department: '',
}
const addressPickList = ref<
  {
    label: string
    value: string
    disabled?: boolean
  }[]
>([])

const model = reactive<{
  readReadPower: number
  readWritePower: number
  scanPower: number
}>({
  readReadPower: 500,
  readWritePower: 2000,
  scanPower: 3000,
})

interface RFIDData {
  data: string
  success: boolean
}

function getSavedPowerValue() {
  uni.getStorage({
    key: 'o_powerValue',
    success(res) {
      // 将res.data按逗号拆分
      const [readReadPower, readWritePower, scanPower] = res.data.split(',')
      model.readReadPower = Number(readReadPower)
      model.readWritePower = Number(readWritePower)
      model.scanPower = Number(scanPower)
      console.log(model.scanPower)
      /*      modal.toast({
        message: model.scanPower,
        duration: 2.5,
      }) */
      const setRet: RFIDData = rfidModule.setAntiWRPower({
        readPower: model.scanPower,
        writePower: model.readWritePower,
      })
      if (!setRet.success) {
        modal.toast({
          message: '设置功率失败',
          duration: 2.5,
        })
      }
    },
    fail(err) {
      console.log(err)
      const setRet: RFIDData = rfidModule.setAntiWRPower({
        readPower: model.scanPower,
        writePower: model.readWritePower,
      })
      if (!setRet.success) {
        modal.toast({
          message: '设置功率失败',
          duration: 2.5,
        })
      }
    },
  })
}

function listenKeyCode() {
  let isKeyPressed = false
  plus.key.addEventListener('keydown', (event) => {
    // console.log('按下按键:' + event.keyCode)
    // 触发RFID枪按键
    if (event.keyCode === 138) {
      isKeyPressed = true
    }
  })
  plus.key.addEventListener('keyup', (event) => {
    // console.log('松开按键:' + event.keyCode)
    // 触发RFID枪按键
    if (event.keyCode === 138 && isKeyPressed) {
      // console.log('按下并松开开枪键')
      handleScanBtnClick()
      isKeyPressed = false
    }
  })
}

function listenRfidValue() {
  globalEvent.addEventListener('RendRfidEvent', (e) => {
    /*    modal.toast({
      message: 'RendRfidEvent收到：' + JSON.stringify(e),
      duration: 1.5,
    }) */
    console.log('RendRfidEvent', e)
    // messageQueue.push(e.epcId)
    messageQueue.add(e.epcId)

    // 更新队列统计信息
    queueStats.maxQueueSize = Math.max(queueStats.maxQueueSize, messageQueue.size)
    console.log(`队列大小: ${messageQueue.size}, 历史最大: ${queueStats.maxQueueSize}`)
  })
}

// 创建设备代码到设备对象的映射，提高查找效率
const equipmentMap = computed(() => {
  const map = new Map()
  activeEquipmentList.value.forEach((equ) => {
    map.set(equ.code, equ)
  })
  return map
})

function getShowListData() {
  if (!activeAddressItem.value) {
    console.log('activeAddressItem.value为空，无法获取显示列表')
    return
  }

  // 使用Map进行O(1)查找，而不是O(n)的find操作
  switch (tab.value) {
    case Tab.planCount:
      showList.value = activeAddressItem.value.planCodeArr.map((item) => {
        return equipmentMap.value.get(item)!
      })
      break
    case Tab.completedCount:
      showList.value = activeAddressItem.value.completedCodeArr.map((item) => {
        return equipmentMap.value.get(item)!
      })
      break
    case Tab.unfinishedCount:
      showList.value = activeAddressItem.value.unfinishedCodeArr.map((item) => {
        return equipmentMap.value.get(item)!
      })
      break
    case Tab.unplannedCount:
      showList.value = activeAddressItem.value.unplannedCodeArr.map((item) => {
        return equipmentMap.value.get(item)
      })
      break
    default:
      break
  }
}

function getAddressData() {
  // console.log('activeAddressList.value', activeAddressList.value)
  // 指针指向地址
  const foundAddress = activeAddressList.value.find(
    item => item.id === activeAddressId.value,
  )
  if (foundAddress) {
    activeAddressItem.value = foundAddress
  }
  else {
    uni.switchTab({
      url: '/pages/makeInventory/index',
    })
  }
}

function handleResetCheck() {
  message
    .confirm({
      msg: '将会重置本地点清点数据',
      title: '是否重新清点',
    })
    .then(() => {
      activeAddressItem.value.completedCodeArr = []
      activeAddressItem.value.unfinishedCodeArr = [
        ...activeAddressItem.value.planCodeArr,
      ]
      activeAddressItem.value.unplannedCodeArr = []
      if (activeAddressItem.value.planCodeArr.length === 0) {
        activeAddressItem.value.completedPercent = 100
      }
      else {
        activeAddressItem.value.completedPercent = 0
      }
      getShowListData()
    })
    .catch(() => { })
}

function handleFinishCheck() {
  // console.log('removeEventListener')
  // plus.key.removeEventListener('keydown', () => {})
  /*  uni.switchTab({
    url: '/pages/makeInventory/index',
  }) */
  uni.navigateBack()
}

function handleSelectTab(v: Tab) {
  tab.value = v
}

/* // 消息队列处理逻辑
async function processQueue() {
  while (isStartHandleQueue.value) {
    if (messageQueue.length > 0) {
      const msgCode = messageQueue.shift() // 从队列中取出一条消息
      if (msgCode) {
        // 处理消息
        console.log(msgCode)
        processingData(msgCode) // 确保 processingData 是异步函数
      }
    }
    // 使用 setTimeout 确保渲染不卡顿
    await new Promise((resolve) => setTimeout(resolve, 20)) // 16ms 大约等于 60fps
  }
} */
// 消息队列处理逻辑
async function processQueue() {
  while (isStartHandleQueue.value) {
    if (messageQueue.size > 0) {
      const startTime = performance.now()

      // 使用 Set 的 values() 方法获取第一个元素，更高效
      const msgCode = messageQueue.values().next().value
      // 删除第一个元素
      messageQueue.delete(msgCode)
      // 处理消息
      console.log('msgCode', msgCode)
      processingData(msgCode) // 确保 processingData 是异步函数

      // 更新处理统计
      const processTime = performance.now() - startTime
      queueStats.totalProcessed++
      queueStats.lastProcessTime = processTime
      queueStats.averageProcessTime
        = (queueStats.averageProcessTime * (queueStats.totalProcessed - 1) + processTime) / queueStats.totalProcessed

      console.log(`处理时间: ${processTime.toFixed(2)}ms, 平均: ${queueStats.averageProcessTime.toFixed(2)}ms`)
    }
    // 使用 setTimeout 确保渲染不卡顿
    await new Promise(resolve => setTimeout(resolve, 50)) // 50ms 提供更好的响应性
  }
}

/**
 * 判断字符串是否以 shortStr 结尾
 * @param shortStr
 * @param longStr 24位
 * 例如0040446546546661，写入RFID时是24位数的"00000040446546546661"，所以要用正则判断
 */
function endsWithString(shortStr: string, longStr: string) {
  // 构建正则表达式，\b 表示单词边界，$ 表示字符串的结束
  const regex = new RegExp(`${shortStr}$`)
  return regex.test(longStr)
}

function processingData(equLongCode: string) {
  if (!activeAddressItem.value) {
    console.log('activeAddressItem.value为空，无法处理扫描数据')
    return
  }

  // 找出包含equCode的equItem
  const tempEquItem = activeEquipmentList.value.find(item =>
    endsWithString(item.code, equLongCode),
  )

  // 判断equCode是否在任务中
  if (tempEquItem !== undefined) {
    // 获取非24位的equCode
    const equCode = tempEquItem.code
    let needsUpdate = false

    if (activeAddressItem.value.planCodeArr.includes(equCode)) {
      // 在本地址计划清点中
      if (!activeAddressItem.value.completedCodeArr.includes(equCode)) {
        // 未登记到已清点，则添加
        activeAddressItem.value.completedCodeArr.push(equCode)
        // 使用更高效的方式移除元素
        const unfinishedIndex = activeAddressItem.value.unfinishedCodeArr.indexOf(equCode)
        if (unfinishedIndex > -1) {
          activeAddressItem.value.unfinishedCodeArr.splice(unfinishedIndex, 1)
        }
        needsUpdate = true
      }
    }
    else {
      // 不在本地址清点计划中
      if (!activeAddressItem.value.unplannedCodeArr.includes(equCode)) {
        // 缓存activeTask查找结果，避免重复查找
        const activeTask = taskDataDb.value.find(
          item => item.id === activeAddressItem.value.taskId,
        )
        // 是否已经解决过冲突，解决过则不再添加到额外发现
        if (activeTask && !activeTask.conflictResolvedEquCodeArr.includes(equCode)) {
          // 添加到额外发现
          activeAddressItem.value.unplannedCodeArr.push(equCode)
          needsUpdate = true
        }
      }
    }

    // 只有在数据发生变化时才重新计算和更新
    if (needsUpdate) {
      // 重新计算百分比
      if (activeAddressItem.value.planCodeArr.length === 0) {
        activeAddressItem.value.completedPercent = 100
      }
      else {
        activeAddressItem.value.completedPercent = Math.round(
          (activeAddressItem.value.completedCodeArr.length
            / activeAddressItem.value.planCodeArr.length)
          * 100,
        )
      }
      // 只在需要时更新显示列表
      getShowListData()
    }
  }
}

function handleScanBtnClick() {
  if (isStartHandleQueue.value) {
    isStartHandleQueue.value = false
    // 停止扫描
    rfidModule.stopReadRfid()
  }
  else {
    isStartHandleQueue.value = true
    // 开始扫描
    // 不应过度的调整以下参数尽最在回调丽教中先保存，并用其他线程外理业各谡辑
    rfidModule.startReadRfid({
      sleep: 3, // 毫秒，读取后暂停时间
      readtime: 50, // 毫秒，rfid读取时间
    })
    processQueue()
  }
  // startHardwareSimulation()
}

function handleModifyItem(data: EquipmentForm) {
  // 缓存修改前数据
  formModelCopy.name = data.name
  formModelCopy.addressName = data.addressName
  formModelCopy.modelType = data.modelType
  formModelCopy.userName = data.userName
  formModelCopy.department = data.department
  console.log('formModel.id = data.id')
  formModel.id = data.id
  formModel.code = data.code
  formModel.name = data.name
  formModel.addressId = data.addressId
  formModel.addressName = data.addressName
  formModel.modelType = data.modelType
  formModel.userName = data.userName
  formModel.department = data.department
  // 开启编辑
  isShowEditor.value = true
}
function closeEditor() {
  isShowEditor.value = false
  isShowPicker.value = false
}

function pickChange() {
  formModel.addressName = addressPickList.value.find(
    item => item.value === formModel.addressId,
  ).label
}

function handleSubmit() {
  form.value
    .validate()
    .then(({ valid }) => {
      if (valid) {
        // modifyData 要修改的各字段数据
        const modifyData: OptionalEquipmentFormBase = {}
        if (formModel.addressName !== formModelCopy.addressName) {
          modifyData.hadModify_address = true
          // 判断地址库是否有这个地址，如果有则获取id
          const addressItem = activeAddressList.value.find(
            item => item.name === formModel.addressName,
          )
          if (addressItem?.id) {
            // 有地址id
            modifyData.addressId = addressItem?.id
          }
          else {
            // 新建地址
            modifyData.addressId = addAddress(
              activeTaskId.value,
              formModel.addressName,
              formModel.code,
            )
          }
          modifyData.addressName = formModel.addressName
        }
        if (formModel.name !== formModelCopy.name) {
          modifyData.name = formModel.name
          modifyData.hadModify_name = true
        }
        if (formModel.modelType !== formModelCopy.modelType) {
          modifyData.modelType = formModel.modelType
          modifyData.hadModify_modelType = true
        }
        if (formModel.userName !== formModelCopy.userName) {
          modifyData.userName = formModel.userName
          modifyData.hadModify_userName = true
        }
        if (formModel.department !== formModelCopy.department) {
          modifyData.department = formModel.department
          modifyData.hadModify_department = true
        }
        if (updateEqu(formModel.id, modifyData, modifyData.hadModify_address)) {
          toast.success('修改成功')
          isShowEditor.value = false
          isShowPicker.value = false

          reGetData()
        }
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

/**
 * 重新获取数据，不知道为什么指针数据没更新
 */
function reGetData() {
  // console.log('activeAddressList', activeAddressList.value)
  // 如果新建了地址，要重新获取地址列表，picker数据才有新的
  activeAddressList.value = addressDataDb.value.filter(
    item => item.taskId === activeTaskId.value,
  )
  // 需要更新指针数据，未知为什么要更新才行
  activeEquipmentList.value = equDataDb.value.filter(parItem =>
    activeAddressList.value.some(item => item.id === parItem.addressId),
  )
  getAddressData()
  getShowListData()
}

function handleShowAddressPick() {
  if (!isShowPicker.value) {
    // 打开选择框
    addressPickList.value = activeAddressList.value.map(item => ({
      label: item.name,
      value: item.id,
    }))
  }
  isShowPicker.value = !isShowPicker.value
}

function handleIsHere(data: EquipmentForm, isHere: boolean) {
  if (isHere) {
    message
      .confirm({
        msg: `将"${data.name}"划归到此地`,
        title: '请确认',
      })
      .then(() => {
        const params = {
          hadModify_address: true,
          addressName: activeAddressItem.value.name,
          addressId: activeAddressItem.value.id,
        }
        if (updateEqu(data.id, params, true)) {
          reGetData()
        }
      })
  }
  else {
    message
      .confirm({
        msg: `从此地的额外发现清掉"${data.name}"`,
        title: '请确认',
      })
      .then(() => {
        activeAddressItem.value.unplannedCodeArr
          = activeAddressItem.value.unplannedCodeArr.filter(
            item => item !== data.code,
          )
        getShowListData()
      })
  }
}

watch(tab, () => {
  getShowListData()
})

onShow(() => {
  // console.log('startCheckPage.show')
  getAddressData()
  getShowListData()
  getSavedPowerValue()
  listenKeyCode()
  listenRfidValue()
})

/* onUnmounted(() => {
  console.log('onUnmounted')
  isStartHandleQueue.value = false
}) */
onUnload(() => {
  plus.key.removeEventListener('keydown', () => { })
  isStartHandleQueue.value = false
  // 停止扫描
  rfidModule.stopReadRfid()
  // console.log('onUnload')
})

onHide(() => {
  // 中途切到桌面，需要重新启动扫描，
  // console.log('onHide')
  plus.key.removeEventListener('keydown', () => { })
  isStartHandleQueue.value = false
  // 停止扫描
  rfidModule.stopReadRfid()
})
</script>

<template>
  <view class="o-shadow fixed left-0 top-0 z-1 w-full bg-white">
    <view class="flex p-4 space-x-2">
      <wd-button plain type="error" @click="handleResetCheck">
        重新清点
      </wd-button>
      <wd-button class="ml-2 grow-1" @click="handleFinishCheck">
        更换地点
      </wd-button>
    </view>
    <view class="px-4 pb-4">
      <view class="flex items-center gap-2">
        <wd-icon name="home" size="22px" />
        <view class="f-h-name o-inline-2 my-2 text-xl font-bold">
          {{ activeAddressItem?.name }}
        </view>
      </view>
      <view class="mt-2 flex items-center justify-between">
        <view>
          <view :class="tab === Tab.planCount ? 'f-select' : ''" class="f-tab" @click="handleSelectTab(Tab.planCount)">
            <view class="f-tab-name">
              计划清点：
            </view>
            <view>{{ activeAddressItem?.planCodeArr?.length }}</view>
          </view>
          <view
            :class="tab === Tab.completedCount ? 'f-select' : ''" class="f-tab"
            @click="handleSelectTab(Tab.completedCount)"
          >
            <view class="f-tab-name">
              已清点：
            </view>
            <view>{{ activeAddressItem?.completedCodeArr?.length }}</view>
          </view>
          <view
            :class="tab === Tab.unfinishedCount ? 'f-select' : ''" class="f-tab"
            @click="handleSelectTab(Tab.unfinishedCount)"
          >
            <view class="f-tab-name">
              未清点：
            </view>
            <view>{{ activeAddressItem?.unfinishedCodeArr?.length }}</view>
          </view>
          <view
            :class="tab === Tab.unplannedCount ? 'f-select' : ''" class="f-tab"
            @click="handleSelectTab(Tab.unplannedCount)"
          >
            <view class="f-tab-name">
              额外发现：
            </view>
            <view
              :class="activeAddressItem?.unplannedCodeArr?.length > 0 ? 'color-red' : ''
              "
            >
              {{ activeAddressItem?.unplannedCodeArr?.length }}
            </view>
          </view>
        </view>
        <wd-circle
          v-model="activeAddressItem.completedPercent"
          :color="rgbaToHex(getColor(activeAddressItem?.completedPercent))" :stroke-width="30" :speed="500"
          :text="`${activeAddressItem?.completedPercent}%`" class="mr-4 shrink-0"
        />
      </view>
    </view>
  </view>
  <view class="px-3 pb-20 pt-68">
    <view v-for="item in showList" :key="item?.id" class="o-shadow mb-2 rd-2 bg-white">
      <view class="p-4">
        <view class="mt-2 color-gray">
          {{ item?.code }}
        </view>
        <view class="flex items-center gap-2 py-2">
          <wd-icon name="discount" size="22px" />
          <view class="py-2 text-xl font-bold">
            {{ item?.name }}
          </view>
        </view>
        <view class="f-babel">
          <view class="f-label-name">
            规格型号：
          </view>
          <view>{{ item?.modelType }}</view>
        </view>
        <view class="f-babel">
          <view class="f-label-name">
            使用人：
          </view>
          <view>{{ item?.userName }}</view>
        </view>
        <view class="f-babel">
          <view class="f-label-name">
            使用部门：
          </view>
          <view>{{ item?.department }}</view>
        </view>
        <view class="f-babel">
          <view class="f-label-name">
            存放地点：
          </view>
          <view>{{ item?.addressName }}</view>
        </view>
      </view>
      <view class="mt-1 flex items-center justify-between px-4 pb-4">
        <!--        <view
          class="o-btn mr-2 o-border color-red"
          @click="handleDelEqu(item.id, item.name)"
        >
          删除
        </view> -->
        <view class="o-btn o-border mr-2 text-primary" @click="handleModifyItem(item)">
          修改
        </view>
        <view v-if="tab === Tab.unplannedCount" class="flex items-center">
          <view class="color-gray">
            是否在此地：
          </view>
          <view class="o-btn mr-2 bg-yellow text-white" @click="handleIsHere(item, false)">
            <wd-icon name="close" size="22px" />
          </view>
          <view class="o-btn bg-green text-white" @click="handleIsHere(item, true)">
            <wd-icon name="check" size="22px" />
          </view>
        </view>
      </view>
    </view>
  </view>
  <view class="fixed bottom-0 left-0 p-4">
    <view :class="isStartHandleQueue ? 'bg-red' : 'bg-primary'" class="o-btn text-white" @click="handleScanBtnClick">
      {{ isStartHandleQueue ? '停止扫描' : '开始扫描' }}
    </view>
  </view>
  <wd-action-sheet v-model="isShowEditor" :title="formModel.code" @close="closeEditor">
    <view class="flex flex-col justify-between" style="height: 86vh">
      <wd-form ref="form" :model="formModel">
        <wd-cell-group border>
          <wd-input
            v-model="formModel.name" :rules="[{ required: true, message: '请填写资源名称' }]" clearable label="资源名称"
            label-width="80px" prop="name"
          />
          <wd-input
            v-model="formModel.modelType" :rules="[{ required: true, message: '请填写规格型号' }]" clearable
            label="规格型号" label-width="80px" prop="modelType"
          />
          <wd-input
            v-model="formModel.userName" :rules="[{ required: true, message: '请填写使用人' }]" clearable label="使用人"
            label-width="80px" prop="userName"
          />
          <wd-input
            v-model="formModel.department" :rules="[{ required: true, message: '请填写使用部门' }]" clearable
            label="使用部门" label-width="80px" prop="department"
          />
          <view class="flex items-center">
            <wd-input
              v-model="formModel.addressName" :rules="[{ required: true, message: '请填写存放地点' }]" clearable
              label="存放地点" label-width="80px" prop="addressName"
            />
            <view class="o-btn o-border mr-2 shrink-0" type="primary" @click="handleShowAddressPick">
              选择
            </view>
          </view>
          <wd-picker-view
            v-if="isShowPicker" v-model="formModel.addressId" :columns="addressPickList"
            @change="pickChange"
          />
        </wd-cell-group>
      </wd-form>
      <view class="p-3">
        <wd-button block size="large" type="primary" @click="handleSubmit">
          保存
        </wd-button>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style lang="scss" scoped>
.f-tab {
  @apply flex items-baseline rd-1 px-4 py-1;

  min-width: 45vw;

  &.f-select {
    @apply bg-primary text-white;

    .f-tab-name {
      @apply text-white;
    }

    .color-red {
      @apply text-white;
    }
  }
}

.f-tab-name {
  @apply shrink-0 color-gray;

  width: 24vw;
}

.f-babel {
  @apply flex mb-1 items-baseline;
}

.f-label-name {
  @apply shrink-0 color-gray;

  width: 24vw;
}

.f-h-name {
  max-width: 80vw;
  max-height: 2.6em;
  overflow: hidden !important;
}
</style>
