<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    navigationBarTitleText: '建立数据',
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
  },
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import NewPrintRfidPage from '@/components/index/newPrintRfidPage.vue'
import NewTaskPage from '@/components/index/newTaskPage.vue'
import { authorizationStore } from '@/store/authorizationStore'
import { stepStore } from '@/store/stepStore'
import { CreateStep } from '@/utils/enum'

defineOptions({
  name: 'Home',
})
const useAuthorizationStore = authorizationStore()
const { haveAuthorization, sn } = storeToRefs(useAuthorizationStore)

const useStepStore = stepStore()
const { createStep } = storeToRefs(useStepStore)

// onShow(() => {
//   // 本页关闭RFID
//   rfidModule.toDisConnectDevice({}, (ret) => {})
// })
</script>

<template>
  <view v-if="haveAuthorization" class="w-full overflow-hidden">
    <NewTaskPage v-if="createStep === CreateStep.createTask" />
    <NewPrintRfidPage v-else-if="createStep === CreateStep.createScanList" />
  </view>
  <wd-overlay :show="!haveAuthorization">
    <view class="mt-40 text-center text-4xl text-white">
      设备未授权
    </view>
    <view class="text-center text-white">
      sn：{{ sn }}
    </view>
  </wd-overlay>
</template>

<style lang="scss" scoped></style>
