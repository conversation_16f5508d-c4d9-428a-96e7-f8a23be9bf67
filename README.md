<p align="center">
  <a href="https://unibest.tech/" target="_blank">📖 文档地址(new)</a>
  <span style="margin:0 10px;">|</span>
  <a href="https://feige996.github.io/hello-unibest/" target="_blank">📱 DEMO 地址</a>
</p>

---

## ⚙️ 环境

- node>=18
- pnpm>=7.30
- Vue Official>=2.1.10
- TypeScript>=5.0

## &#x1F4C2; 快速开始

执行 `pnpm create unibest` 创建项目

执行 `pnpm i` 安装依赖

执行 `pnpm dev` 运行 `H5`

## 📦 运行（支持热更新）

- web平台： `pnpm dev:h5`, 然后打开 [http://localhost:9000/](http://localhost:9000/)。
- weixin平台：`pnpm dev:mp-weixin` 然后打开微信开发者工具，导入本地文件夹，选择本项目的`dist/dev/mp-weixin` 文件。
- APP平台：`pnpm dev:app`, 然后打开 `HBuilderX`，导入刚刚生成的`dist/dev/app` 文件夹，选择运行到模拟器(开发时优先使用)，或者运行的安卓/ios基座。

## 🔗 发布

- web平台： `pnpm build:h5`，打包后的文件在 `dist/build/h5`，可以放到web服务器，如nginx运行。如果最终不是放在根目录，可以在 `manifest.config.ts` 文件的 `h5.router.base` 属性进行修改。
- weixin平台：`pnpm build:mp-weixin`, 打包后的文件在 `dist/build/mp-weixin`，然后通过微信开发者工具导入，并点击右上角的“上传”按钮进行上传。
- APP平台：`pnpm build:app`, 然后打开 `HBuilderX`，导入刚刚生成的`dist/build/app` 文件夹，选择发行 - APP云打包。

## 提交

- `feat: 新功能xxx`

- feat: 新功能 (feature)
- fix: 修复问题 (bug fix)
- docs: 文档 (documentation)
- style: 代码风格相关（不影响运行结果的变动）
- perf: 性能优化 (performance improvement)
- test: 测试相关 (tests)
- chore: 构建过程或辅助工具的变动
- refactor: 代码重构（既没有修复 bug 也没有添加新功能）
- revert: 撤销修改
- build: 构建工具或外部依赖的改动
- ci: 持续集成 (continuous integration)
- wip: 开发中 (work in progress)
- workflow: 工作流改进
- types: 类型定义文件更改
- release: 发布版本

## 接口需求

### 1. 获取待新建任务列表

POST请求：

```ts
url：'api/getTaskList',
返回： {
  success: 1,
  data [
    {
      taskId: string,
      taskName: string,
    }
  ]
}
```

### 2. 根据id获取任务

POST请求：

```ts
url：'api/getTask',
taskId: string,

返回： {
  success: 1,
  taskId: string,
  taskName: string,
  data [
    [
      "资产名称1",
      "使用人1",
      "使用部门1",
      "规格型号1",
      "存放地点1",
      "条形码1"
    ],
    [
      "资产名称2",
      "使用人2",
      "使用部门2",
      "规格型号2",
      "存放地点2",
      "条形码2"
    ]
  ]
}
```

### 3. 表头协定

POST请求：

```ts
url：'api/title',
title: [
'资产名称',
'规格型号',
'条形码',
'使用人',
'存放地点',
'使用部门',
'是否清点',
'是否更变资产名',
'是否变更规格型号',
'是否变更使用人',
'是否变更存放地点',
'是否变更使用部门',
]

返回：{success: 1}
```

### 4. 表格内容

POST请求：

```ts
url：'api/table',
taskId: string,
table: [
 ['电脑1','型号1'...],
 ['电脑2','型号1'...],
]

返回：{success: 1}
```
