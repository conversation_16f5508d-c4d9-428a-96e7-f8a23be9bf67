<template>
  <view
    class="f-btn o-btn bg-primary text-white text-xl py-6"
    @click="renderJS.createFileInputDom"
  >
    <view class="font-bold">本地</view>
    <view class="">导入</view>
  </view>
</template>
<script>
export default {
  data() {
    return {}
  },
  methods: {
    sendData(d) {
      this.$emit('readFileFinish', d)
    },
  },
}
</script>
<script module="renderJS" lang="renderjs">
import {
	read,
	utils
} from 'xlsx';
export default {
	methods: {
		createFileInputDom(e, ownerVm) {
			let fileInput = document.createElement('input')
			fileInput.setAttribute('type', 'file')
			fileInput.setAttribute('accept', '*')
			fileInput.click()
			fileInput.addEventListener('change', e => {
				let file = e.target.files[0]
				// #ifdef APP-PLUS
				let reader = new FileReader();
				reader.readAsBinaryString(file);
				// reader.readAsDataURL(file);
				reader.onload = function(event) {
					const ab = event.target.result; // 文件的base64
					// ownerVm.callMethod('parseJSONData', base64Str)

					// 如果需要得到文件的本地路径，可以通过下面方法
					// ownerVm.callMethod('receiveRenderFile', {
					// 	name: file.name,
					// 	filePath: base64Str
					// })

					const workbook = read(ab, {
						type: 'binary'
					});
					/* generate array of presidents from the first worksheet */
					const ws = workbook.Sheets[workbook.SheetNames[0]]; // get the first worksheet
					const xData = utils.sheet_to_json(ws); // generate objects
					ownerVm.callMethod("sendData", xData);
					/* update state */
					// console.log(xData)
					// fileInput.value = null
				}
				// #endif

				// #ifdef H5
				// 如果需要得到文件的本地路径，可以通过下面方法
				// const filePath = URL.createObjectURL(file)
				// ownerVm.callMethod('receiveRenderFile', {
				// 	name: file.name,
				// 	filePath: filePath
				// })
				// #endif
			})
		}
	}
}
</script>
<style>
.f-btn-box {
  margin-top: 30vh;
}

.f-btn {
  width: 20vw;
}
</style>
