<template>
  <view class="main">
    <button class="selectBtn" size="default" type="primary" @click="renderJS.createFileInputDom">
      选择文件
    </button>
  </view>
</template>
<script>
export default {
  data() {
    return {}
  },
  onLoad(res) {},
  methods: {
    // plus.io选择文件
    // 选择完文件后，拿到的是base64字符串，转成对应的数据
    parseJSONData(base64Str) {
      let jsonStr = this.convertBase64ToUTF8(base64Str)
      if (base64Str.includes('application/json')) {
        let jsonData = JSON.parse(jsonStr)
        this.$emit('readJSONFinish', { jsonData })
      } else {
        this.$emit('readJSONFinish', { jsonStr })
      }
    },

    convertBase64ToUTF8(base64Str) {
      let base64Content = atob(base64Str.split(',')[1])
      base64Content = base64Content
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join('')
      let jsonStr = decodeURIComponent(base64Content)
      return jsonStr
    },

    async receiveRenderFile(result) {
      // #ifdef APP-PLUS
      const fileUrl = await this.base64toPath(result.filePath, result.name)
      this.fileName = fileUrl.relativePath
      this.filePath = fileUrl.localAbsolutePath
      // #endif
      // #ifdef H5
      this.fileName = result.name
      this.filePath = result.filePath
      // #endif
      console.log('选择文件的路径：' + this.filePath)
    },

    //将base64转成路径
    async base64toPath(base64, attachName) {
      console.log('base64开始转化成文件')
      let _that = this
      return new Promise(function (resolve, reject) {
        const filePath = `_doc/yourFilePath/${attachName}`
        plus.io.resolveLocalFileSystemURL(
          '_doc',
          function (entry) {
            entry.getDirectory(
              'yourFilePath',
              {
                create: true,
                exclusive: false,
              },
              function (entry) {
                entry.getFile(
                  attachName,
                  {
                    create: true,
                    exclusive: false,
                  },
                  function (entry) {
                    entry.createWriter(function (writer) {
                      writer.onwrite = function (res) {
                        console.log('base64转化文件完成')
                        const obj = {
                          relativePath: filePath,
                          localAbsolutePath: plus.io.convertLocalFileSystemURL(filePath),
                        }
                        resolve(obj)
                      }
                      writer.onerror = reject
                      writer.seek(0)
                      writer.writeAsBinary(_that.getSymbolAfterString(base64, ','))
                    }, reject)
                  },
                  reject,
                )
              },
              reject,
            )
          },
          reject,
        )
      })
    },
    // 取某个符号后面的字符
    getSymbolAfterString(val, symbolStr) {
      if (val == undefined || val == null || val == '') {
        return ''
      }
      val = val.toString()
      const index = val.indexOf(symbolStr)
      if (index != -1) {
        val = val.substring(index + 1, val.length)
        return val
      } else {
        return val
      }
    },
  },
}
</script>
<script module="renderJS" lang="renderjs">
export default {
	data() {
		return {}
	},
	mounted() {

	},
	methods: {
		createFileInputDom(e, ownerVm) {
			let fileInput = document.createElement('input')
			fileInput.setAttribute('type', 'file')
			fileInput.setAttribute('accept', '*')
			fileInput.click()
			fileInput.addEventListener('change', e => {
				let file = e.target.files[0]
				// #ifdef APP-PLUS
				let reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = function(event) {
					const base64Str = event.target.result; // 文件的base64
					ownerVm.callMethod('parseJSONData', base64Str)

                    // 如果需要得到文件的本地路径，可以通过下面方法
                    // ownerVm.callMethod('receiveRenderFile', {
                    // 	name: file.name,
                    // 	filePath: base64Str
                    // })
				}
				// #endif

				// #ifdef H5
                // 如果需要得到文件的本地路径，可以通过下面方法
				// const filePath = URL.createObjectURL(file)
                // ownerVm.callMethod('receiveRenderFile', {
                // 	name: file.name,
                // 	filePath: filePath
                // })
				// #endif
			})
		}
	}
}
</script>
<style>
.main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-y: scroll;
}

.selectBtn {
  border-radius: 10rpx;
  font-size: 32rpx;
}
</style>
