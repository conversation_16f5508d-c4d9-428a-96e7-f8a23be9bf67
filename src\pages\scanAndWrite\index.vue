<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '扫码并写入',
  },
}
</route>
<script lang="ts" setup>
import { authorizationStore } from '@/store/authorizationStore'
import { storeToRefs } from 'pinia'

const rfidModule = uni.requireNativePlugin('rfidModule')
const qrScanModule = uni.requireNativePlugin('qrScanModule') // 扫描仪Module
const globalEvent = uni.requireNativePlugin('globalEvent')
const modal = uni.requireNativePlugin('modal')

const useAuthorizationStore = authorizationStore()
const { haveAuthorization, sn } = storeToRefs(useAuthorizationStore)

const scanResult = ref('')
const isScanSuccess = ref(false) // 是否扫码成功
const isWriteSuccess = ref(false) // 是否写入成功
const isShowManualInput = ref(false) // 手动输入是否弹出
const delayShow = ref(false) // 成功延时消失
const inputCode = ref('') // 手动输入的条码编码
const model = reactive<{
  readReadPower: number
  readWritePower: number
  scanPower: number
}>({
  readReadPower: 500,
  readWritePower: 2000,
  scanPower: 3000,
})
type RFIDData = {
  data: string
  success: boolean
}

onLoad(() => {
  getSavedPowerValue()
  initQrScan()
  listenKeyCode()
  listenQrCode()
})

const initQrScan = () => {
  // 初始化扫描仪
  qrScanModule.initQrScan({}, (ret) => {
    if (!ret.success) {
      modal.toast({
        message: '扫描枪初始化失败',
        duration: 1.5,
      })
    }
  })
}

const listenQrCode = () => {
  globalEvent.addEventListener('RendQrScanEvent', function (e) {
    /*    modal.toast({
      message: "RendQrScanEvent收到：" + JSON.stringify(e),
      duration: 1.5
    }); */
    console.log(e.result)
    // 将"012345678901234567890123\r"字符串只保留数字
    scanResult.value = e.result.replace(/[^\d]/g, '')
    isScanSuccess.value = true
  })
}

const listenKeyCode = () => {
  let isKeyPressed = false

  plus.key.addEventListener('keydown', (event) => {
    console.log(rfidModule)
    console.log('按下按键:' + event.keyCode)
    // 触发RFID枪按键
    if (event.keyCode === 138) {
      isKeyPressed = true
    }
  })

  plus.key.addEventListener('keyup', (event) => {
    console.log('松开按键:' + event.keyCode)
    if (event.keyCode === 138 && isKeyPressed) {
      console.log('按下并松开开枪键')
      if (isShowManualInput.value) {
        handleManualSummit()
        return
      }
      if (isWriteSuccess.value) {
        // 已写入成功
        // 按下扳机重置isScanSuccess，让下次按下扳机键能重新扫码
        isScanSuccess.value = false
        isWriteSuccess.value = false
        setTimeout(() => {
          delayShow.value = false
        }, 2000)
      } else {
        // 未写入
        if (isScanSuccess.value) {
          // 写RFID
          toWriteRfid()
        } else {
          // 扫码
          toScan()
        }
      }
      isKeyPressed = false
    }
  })
}

const getSavedPowerValue = () => {
  uni.getStorage({
    key: 'o_powerValue',
    success: function (res) {
      console.log(res.data)
      // 将res.data按逗号拆分
      const [readReadPower, readWritePower, scanPower] = res.data.split(',')
      model.readReadPower = Number(readReadPower)
      model.readWritePower = Number(readWritePower)
      model.scanPower = Number(scanPower)
    },
  })
}

const toScan = () => {
  // 异步
  qrScanModule.toScan({}, () => {
    // 等listenQrcode监听到扫码事件后，再执行写RFID操作
  })
}

const toWriteRfid = () => {
  // 设置读写功率
  const setRet: RFIDData = rfidModule.setAntiWRPower({
    readPower: model.readReadPower,
    writePower: model.readWritePower,
  })
  if (setRet.success) {
    // 异步调用 ，可以通过扫描枪扫出条形码，并将条形码处理成24位字符，并写入RFID标签
    // 将scanResult补足24为，前面加0
    const epcId = scanResult.value.padStart(24, '0')

    console.log(epcId)
    // 写入必须为24位长度，如果条形码长度不足24位，自己在前面补0，并处理后续业务时注意该逻辑
    rfidModule.writeUpdateRfid(
      {
        epcId,
      },
      (writeRet: { success: boolean }) => {
        if (writeRet.success) {
          // 验证是否写入正确
          rfidModule.writeReadRfid({}, (readRet: RFIDData) => {
            if (readRet.success) {
              if (readRet.data === epcId) {
                isWriteSuccess.value = true
                delayShow.value = true
                modal.toast({
                  message: '写入成功',
                  duration: 1.5,
                })
              } else {
                modal.toast({
                  message: '写入失败，请重新写入',
                  duration: 1.5,
                })
              }
            } else {
              modal.toast({
                message: '未能验证写入是否正确，请靠近标签',
                duration: 3,
              })
            }
          })
        }
      },
    )
  }
}

const manualInputShow = () => {
  isShowManualInput.value = true
}

const handleOverlayClick = () => {
  isShowManualInput.value = false
  inputCode.value = ''
}

const handleManualSummit = () => {
  scanResult.value = inputCode.value
  isShowManualInput.value = false
  isScanSuccess.value = true
  inputCode.value = ''
}

// 监听返回键
onBackPress((options) => {
  // 自定义返回逻辑
  if (options.from === 'backbutton' || options.from === 'navigateBack') {
    // 这里可以添加提示用户的逻辑
    plus.key.removeEventListener('keydown', () => {})
    globalEvent.removeEventListener('RendQrScanEvent', () => {})
    // uni.navigateBack()
    // return true; // 阻止默认返回
  }
  return false
})
</script>
<template>
  <wd-overlay :show="!haveAuthorization">
    <view class="text-white text-center text-4xl mt-40">设备未授权</view>
    <view class="text-white text-center">sn：{{ sn }}</view>
  </wd-overlay>
  <view class="p-4">
    <text class="font-bold mb-2">操作步骤：</text>
    <ol class="text-sm ml-2 p-2">
      <li>按下扳机键扫条形码。</li>
      <li>
        成功获取条码信息后，将RFID标签贴到感应区内，点击写入键或按下扳机键。
      </li>
      <li>等待成功信息即可。</li>
    </ol>
    <view class="flex full space-x-2 mt-2">
      <wd-button
        :plain="isScanSuccess"
        :type="isScanSuccess ? 'info' : 'primary'"
        class="grow-3"
        @click="toScan"
      >
        {{ isScanSuccess ? '重新扫码' : '按下扳机键扫码条形码' }}
      </wd-button>
      <wd-button
        :plain="true"
        type="info"
        class="grow-1"
        @click="manualInputShow"
      >
        手动输入
      </wd-button>
    </view>
    <view v-if="isScanSuccess">
      <view class="font-bold mt-2 mb-2">已获取条码编号为：</view>
      <view class="text-lg font-bold text-center">{{ scanResult }}</view>
    </view>
    <view v-if="delayShow" class="mt-4">
      <view class="text-center text-4xl font-bold color-green">写入成功</view>
      <view class="text-center">再次按下扳机键可继续扫码</view>
    </view>
    <view class="flex">
      <wd-button
        v-if="isScanSuccess"
        :plain="!(isScanSuccess && !isWriteSuccess)"
        :type="isScanSuccess && !isWriteSuccess ? 'primary' : 'info'"
        class="mt-2 flex-1"
        @click="toWriteRfid"
      >
        {{
          isScanSuccess && !isWriteSuccess
            ? '按下扳机键写入RFID'
            : '重新写入RFID'
        }}
      </wd-button>
    </view>
    <wd-overlay :show="isShowManualInput" @click="handleOverlayClick">
      <view class="f-wrapper">
        <view class="f-box p-4 bg-white rd-2 flex flex-col" @click.stop>
          <wd-input
            type="text"
            v-model="inputCode"
            placeholder="请输入条码编码"
          />
          <wd-button class="mt-4" type="primary" @click="handleManualSummit">
            点击或按下扳机键确认
          </wd-button>
        </view>
      </view>
    </wd-overlay>
  </view>
</template>
<style lang="scss" scoped>
.f-wrapper {
  display: flex;
  justify-content: center;
  height: 100%;
}

.f-box {
  width: 60vw;
  height: fit-content;
  margin-top: 20vh;
}
</style>
