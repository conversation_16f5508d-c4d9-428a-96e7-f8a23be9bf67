import { ColorEnum } from '@/utils/enum'

const removeHash = (hex: string) => (hex.charAt(0) === '#' ? hex.slice(1) : hex)

// eg. 992f2f => r:99 g:2f b:2f a:ff
const parseHex = (nakedHex: string) => {
  const isShort = nakedHex.length === 3 || nakedHex.length === 4
  const twoDigitHexR = isShort
    ? `${nakedHex.slice(0, 1)}${nakedHex.slice(0, 1)}`
    : nakedHex.slice(0, 2)
  const twoDigitHexG = isShort
    ? `${nakedHex.slice(1, 2)}${nakedHex.slice(1, 2)}`
    : nakedHex.slice(2, 4)
  const twoDigitHexB = isShort
    ? `${nakedHex.slice(2, 3)}${nakedHex.slice(2, 3)}`
    : nakedHex.slice(4, 6)
  const twoDigitHexA =
    (isShort
      ? `${nakedHex.slice(3, 4)}${nakedHex.slice(3, 4)}`
      : nakedHex.slice(6, 8)) || 'ff'
  return {
    r: twoDigitHexR,
    g: twoDigitHexG,
    b: twoDigitHexB,
    a: twoDigitHexA,
  }
}

// 16进制转十进制 2f => 47
const hexToDecimal = (hex: string) => parseInt(hex, 16)

// {99,2f,2f,ff} => {153,47,47,1}
const hexesToDecimals = ({ r, g, b, a }: any) => ({
  r: hexToDecimal(r),
  g: hexToDecimal(g),
  b: hexToDecimal(b),
  a: +(hexToDecimal(a) / 255).toFixed(2),
})

const isNumeric = (n: any) => !isNaN(parseFloat(n)) && isFinite(n) // eslint-disable-line no-restricted-globals, max-len

const formatRgb = (decimalObject: any, parameterA?: number) => {
  const { r, g, b, a: parsedA } = decimalObject
  const a = isNumeric(parameterA) ? parameterA : parsedA

  // return `rgba(${r}, ${g}, ${b}, ${a})`;
  return {
    r,
    g,
    b,
    a,
  }
}
const hexToRgba = (hex: string, a?: number) => {
  const hashlessHex = removeHash(hex)
  const hexObject = parseHex(hashlessHex)
  const decimalObject = hexesToDecimals(hexObject)

  return formatRgb(decimalObject, a)
}

/**
 * 计算对应百分比的颜色值
 * @param newV
 * @param oldV
 * @param newP
 * @param oldP
 * @param percentage
 */
const percentageColor = (
  newV: number,
  oldV: number,
  newP: number,
  oldP: number,
  percentage: number,
) => {
  if (newV >= oldV) {
    return (
      (Math.abs(newV - oldV) / Math.abs(newP - oldP)) * (percentage - oldP) +
      oldV
    )
  } else {
    return (
      oldV -
      (Math.abs(newV - oldV) / Math.abs(newP - oldP)) * (percentage - oldP)
    )
  }
}

type colorAndPosition = {
  hex: string
  percent: number
}

/**
 * 获取渐变色在某个百分比的 rgba 色值
 * @param percentage 百分比 0-100,不是小数
 * @param colorAndPosition 从小到大排列的颜色数组[{ hex: "#52A3FF", percent: 0 },{ hex: "#E6374D", percent: 100 },]
 * @param shadow 是否设为阴影，true会给0.2的透明度
 */
export const getColorGradient = (
  percentage: number,
  colorAndPosition: colorAndPosition[],
  shadow = false,
) => {
  const r: number[] = []
  const g: number[] = []
  const b: number[] = []
  const a: number[] = []
  const p: number[] = []
  let cr: number, cg: number, cb: number, ca: number
  if (percentage === colorAndPosition[0].percent) {
    return `rgba(${hexToRgba(colorAndPosition[0].hex).r},
    ${hexToRgba(colorAndPosition[0].hex).g}, ${
      hexToRgba(colorAndPosition[0].hex).b
    }, ${hexToRgba(colorAndPosition[0].hex).a})`
  }

  for (let i = 0; i < colorAndPosition.length; i++) {
    r.push(hexToRgba(colorAndPosition[i].hex).r)
    g.push(hexToRgba(colorAndPosition[i].hex).g)
    b.push(hexToRgba(colorAndPosition[i].hex).b)
    a.push(hexToRgba(colorAndPosition[i].hex).a)
    p.push(colorAndPosition[i].percent)
    if (percentage <= colorAndPosition[i].percent) {
      cr = Math.round(
        percentageColor(r[i], r[i - 1], p[i], p[i - 1], percentage),
      )
      cg = Math.round(
        percentageColor(g[i], g[i - 1], p[i], p[i - 1], percentage),
      )
      cb = Math.round(
        percentageColor(b[i], b[i - 1], p[i], p[i - 1], percentage),
      )
      if (!shadow) {
        ca = Math.abs(
          percentageColor(a[i], a[i - 1], p[i], p[i - 1], percentage),
        )
      } else {
        ca = 0.2
      }
      return `rgba(${cr}, ${cg}, ${cb}, ${ca})`
    }
  }
  return 'rgba(255,255,255,0)'
}

export function addAlpha(color: string, opacity: number): string {
  // coerce values so ti is between 0 and 1.
  const _opacity = Math.round(Math.min(Math.max(opacity || 1, 0), 1) * 255)
  return color + _opacity.toString(16).toUpperCase()
}

export function rgbaToHex(rgba: string) {
  const result = rgba.match(
    /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)/,
  )
  if (!result) {
    throw new Error('Invalid RGBA format')
  }

  const r = parseInt(result[1], 10)
  const g = parseInt(result[2], 10)
  const b = parseInt(result[3], 10)
  const a = result[4] ? Math.round(parseFloat(result[4]) * 255) : 255 // Default alpha is 1

  const hex =
    ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0') +
    (a < 255 ? a.toString(16).padStart(2, '0') : '')
  return `#${hex}`
}

export const getColor = (percentage: number) => {
  let p = percentage
  if (typeof p !== 'number' || isNaN(p)) {
    p = 0
  } else if (p < 0) {
    p = 0
  }
  return getColorGradient(p, [
    { hex: ColorEnum.red, percent: 0 },
    { hex: ColorEnum.red, percent: 20 },
    { hex: ColorEnum.yellow, percent: 70 },
    { hex: ColorEnum.green, percent: 100 },
  ])
}
