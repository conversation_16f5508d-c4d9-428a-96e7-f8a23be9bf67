<script lang="ts" setup>
import type { TaskListRes } from '@/service/api'
import type { TaskForm } from '@/store/dbStore'
import { storeToRefs } from 'pinia'
import { useMessage, useToast } from 'wot-design-uni'
import ChooseFile from '@/components/index/chooseFile.vue'
import { dbHook } from '@/hooks/dbHook'
import { getTaskDetailAPI, getTaskListAPI, setTitleAPI } from '@/service/api'
import { dbStore } from '@/store/dbStore'
import { stepStore } from '@/store/stepStore'
import { CreateStep } from '@/utils/enum'

const message = useMessage()
const toast = useToast()
const useStepStore = stepStore()
const { createStep, activeTaskId, activeTaskName } = storeToRefs(useStepStore)
const useDbStore = dbStore()
const { isOnlineMode } = storeToRefs(useDbStore)

const {
  taskDataDb,
  saveLocalFileData,
  saveNetWorkFileData,
  delTask,
  updateTask,
} = dbHook()

// console.log('taskDataDb', taskDataDb.value)
// console.log('addressDataDb', addressDataDb.value)
// console.log('equDataDb', equDataDb.value)

const taskName = ref('')
const isShowFileInputModal = ref(false)
const isShowNetWorkInput = ref(false)
const baseUrlText = ref('')
const netWorkTaskList = ref<TaskListRes['data']>([])
const netWorkTaskListLoading = ref(false)
const isNetworkModel = ref(false)

// 计算属性：倒序显示任务列表
const reversedTaskDataDb = computed(() => {
  return [...taskDataDb.value].reverse()
})

function handleDelTask(id: string, name: string) {
  message
    .confirm({
      msg: name,
      title: '是否删除任务',
    })
    .then(() => {
      delTask(id)
    })
    .catch(() => { })
}
function handleSelectTask(id: string, name: string) {
  activeTaskId.value = id
  activeTaskName.value = name
  createStep.value = CreateStep.createScanList
}

function handleModifyItem(data: TaskForm) {
  taskName.value = data.name
  message
    .prompt({
      title: '修改任务名',
      inputValue: data.name,
    })
    .then((resp) => {
      updateTask(data.id, `${resp.value}`)
    })
    .catch((error) => {
      console.log(error)
    })
}

function readFileFinish(data: any) {
  saveLocalFileData(data)
  isShowFileInputModal.value = false
}

function handleNetWorkImport() {
  isNetworkModel.value = true
  if (uni.getStorageSync('o_baseUrl') === '') {
    isShowNetWorkInput.value = true
  }
  else {
    getNetWorkTaskList()
  }
}

function handleSaveNetWork() {
  isShowNetWorkInput.value = false
  uni.setStorageSync('o_baseUrl', baseUrlText.value)
  getNetWorkTaskList()
}

function getNetWorkTaskList() {
  netWorkTaskListLoading.value = true
  getTaskListAPI()
    .then((res) => {
      console.log(res)
      if (res.data.success) {
        netWorkTaskList.value = res.data.data
      }
      else {
        toast.error('服务器错误')
        isShowNetWorkInput.value = true
      }
    })
    .catch(() => {
      toast.error('联网失败')
      isShowNetWorkInput.value = true
    })
    .finally(() => {
      netWorkTaskListLoading.value = false
    })
}

function handleSelectNetWorkTask(taskId: string, taskName: string) {
  isNetworkModel.value = false
  activeTaskId.value = taskId
  activeTaskName.value = taskName
  getTaskDetailAPI(taskId)
    .then((res) => {
      if (res.data.success) {
        isOnlineMode.value = true
        saveNetWorkFileData(res.data)
        setTitleAPI()
        isShowFileInputModal.value = false
        toast.success('导入网络任务成功')
      }
      else {
        toast.error('服务器错误')
      }
    })
    .catch(() => {
      toast.error('联网失败')
    })
}

function handleCloseFileInputModal() {
  isShowFileInputModal.value = false
  isNetworkModel.value = false
  isShowNetWorkInput.value = true
}
</script>

<template>
  <wd-toast />
  <wd-message-box />
  <wd-overlay :show="isShowFileInputModal" @click="handleCloseFileInputModal">
    <view class="f-btn-box mx-auto rd-2 bg-white px-4 py-4" @click.stop>
      <div v-if="!isNetworkModel" class="flex justify-center space-x-4">
        <ChooseFile @read-file-finish="readFileFinish" />
        <view class="f-btn o-btn bg-primary py-6 text-xl text-white" @click="handleNetWorkImport">
          <view class="font-bold">
            联网
          </view>
          <view class="">
            导入
          </view>
        </view>
      </div>
      <template v-if="isNetworkModel">
        <template v-if="isShowNetWorkInput">
          <view class="text-sm text-red">
            请先设置正确联网地址
          </view>
          <wd-input v-model="baseUrlText" type="text" class="my-4" placeholder="0.0.0.0:0" clearable />
          <view class="o-btn bg-primary text-white" @click="handleSaveNetWork">
            保存
          </view>
        </template>
        <template v-if="!isShowNetWorkInput">
          <view v-if="netWorkTaskList.length === 0" class="text-sm">
            请选择任务模板导入
          </view>
          <scroll-view class="o-bg-no box-border rd-2 p-2" :scroll-y="true">
            <view v-if="netWorkTaskListLoading" class="box-border w-full center py-8">
              <wd-loading />
            </view>
            <view
              v-for="item in netWorkTaskList" :key="item.taskId" class="my-2 rd-2 bg-white px-2 py-4"
              @click="handleSelectNetWorkTask(item.taskId, item.taskName)"
            >
              {{ item.taskName }}
            </view>
          </scroll-view>
        </template>
      </template>
    </view>
  </wd-overlay>
  <view class="o-shadow fixed left-0 top-0 w-full shrink-0 bg-white">
    <view class="bg-primary w-full center flex-col" style="height: 26vh;">
      <image src="/static/images/bander.png" mode="widthFix" style="width: 80vw;margin-top:5vh;" />
    </view>
    <view class="p-4">
      <view class="o-btn bg-primary text-white" @click="isShowFileInputModal = true">
        新建任务
      </view>
    </view>
  </view>
  <view class="px-3 pt-62 space-y-2" :scroll-y="true">
    <view v-for="item in reversedTaskDataDb" :key="item.id" class="o-shadow rd-2 bg-white">
      <view class="p-4">
        <view class="color-gray">
          创建时间：{{ item.createDate }}
        </view>
        <view class="text-lg font-bold">
          {{ item.name }}
        </view>
      </view>
      <view class="flex justify-between px-4 pb-4">
        <view class="o-btn o-border mr-2 color-red" type="error" plain @click="handleDelTask(item.id, item.name)">
          删除
        </view>
        <view class="o-btn o-border text-primary mr-2" @click="handleModifyItem(item)">
          修改
        </view>
        <view class="o-btn bg-primary grow-1 text-white" @click="handleSelectTask(item.id, item.name)">
          选择任务
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
$topHeight: 20vw;

.f-top-bar {
  height: $topHeight;
}

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.f-btn-box {
  width: 76vw;
  margin-top: 20vh;
}

.f-btn {
  width: 20vw;
}
</style>
