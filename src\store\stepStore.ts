import { defineStore } from 'pinia'
import { ref } from 'vue'
import { CreateStep, ScanStep } from '@/utils/enum'
import { AddressForm } from '@/store/dbStore'

export const stepStore = defineStore(
  'stepStore',
  () => {
    // 新建页面步骤
    const createStep = ref<CreateStep>(CreateStep.createTask)
    // 当前任务id
    const activeTaskId = ref('')
    const activeTaskName = ref('')
    // 当前地址id
    const activeAddressId = ref('')

    return {
      createStep,
      activeTaskId,
      activeTaskName,
      activeAddressId,
    }
  },
  {
    persist: true,
  },
)
