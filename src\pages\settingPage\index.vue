<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '配置',
  },
}
</route>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useMessage, useToast } from 'wot-design-uni'
import { dbHook } from '@/hooks/dbHook'
import { authorizationStore } from '@/store/authorizationStore'
import { dbStore } from '@/store/dbStore'

const message = useMessage()
const { resetDb } = dbHook()
const useDbStore = dbStore()
const { isOnlineMode } = storeToRefs(useDbStore)
const toast = useToast()
const useAuthorizationStore = authorizationStore()
const { sn } = storeToRefs(useAuthorizationStore)

const baseUrlText = ref('')

const powerColumns = [
  500,
  600,
  700,
  800,
  900,
  1000,
  1100,
  1200,
  1300,
  1400,
  1500,
  1600,
  1700,
  1800,
  1900,
  2000,
  2100,
  2200,
  2300,
  2400,
  2500,
  2600,
  2700,
  2800,
  2900,
  3000,
]
const model = reactive<{
  readReadPower: number
  readWritePower: number
  scanPower: number
}>({
  readReadPower: 500,
  readWritePower: 2000,
  scanPower: 3000,
})

// 添加初始化标志，防止初始加载时触发watch
const isInitializing = ref(true)

onMounted(() => {
  baseUrlText.value = uni.getStorageSync('o_baseUrl') || ''
  uni.getStorage({
    key: 'o_powerValue',
    success(res) {
      console.log(res.data)
      // 将res.data按逗号拆分
      const [readReadPower, readWritePower, scanPower] = res.data.split(',')
      model.readReadPower = Number(readReadPower)
      model.readWritePower = Number(readWritePower)
      model.scanPower = Number(scanPower)

      // 初始化完成后设置标志
      nextTick(() => {
        isInitializing.value = false
      })
    },
    fail() {
      // 如果获取失败，也需要设置初始化完成
      isInitializing.value = false
    },
  })
})

const form = ref()

function handleReset() {
  message
    .confirm({
      msg: '请注意导出备份好清点数据',
      title: '是否清空所有数据？',
    })
    .then(() => {
      resetDb()
    })
}

function handleSave() {
  const powerValue
    = `${model.readReadPower},${model.readWritePower},${model.scanPower}`
  try {
    uni.setStorageSync('o_powerValue', powerValue)
    toast.success('保存成功')
  }
  catch (e) {
    toast.success('保存失败')
  }
}

watch(baseUrlText, (newVal) => {
  if (!isInitializing.value) {
    uni.setStorageSync('o_baseUrl', newVal)
  }
})

watch(
  () => model,
  () => {
    // 只有在初始化完成后才触发保存
    if (!isInitializing.value) {
      handleSave()
    }
  },
  { deep: true },
)
</script>

<template>
  <view class="p-4 space-y-4">
    <view class="o-shadow mb-2 rd-2 bg-white p-4">
      <wd-cell-group border>
        <view class="flex justify-between">
          <view class="mb-2 text-lg font-bold">
            联网支持
          </view>
          <view v-if="isOnlineMode" class="text-green">
            已开启
          </view>
          <view v-else class="text-gray">
            未开启
          </view>
        </view>
        <wd-cell title="开启联网支持" center>
          <wd-switch v-model="isOnlineMode" />
        </wd-cell>
        <view class="text-lg font-bold">
          服务地址
        </view>
        <wd-input v-model="baseUrlText" placeholder="0.0.0.0:0" clearable />
      </wd-cell-group>
    </view>
    <view class="o-shadow mb-2 rd-2 bg-white p-4">
      <wd-cell-group border>
        <view class="mb-2 text-lg font-bold">
          RFID写入模式
        </view>
        <wd-picker
          v-model="model.readReadPower"
          :columns="powerColumns"
          label="写功率"
        />
        <wd-picker
          v-model="model.readWritePower"
          :columns="powerColumns"
          label="读功率"
        />
        <view class="mb-2 mt-4 text-lg font-bold">
          盘点模式
        </view>
        <wd-picker
          v-model="model.scanPower"
          :columns="powerColumns"
          label="读功率"
        />
      </wd-cell-group>
    </view>
    <!-- <wd-button class="mt-4" block size="large" @click="handleSave">
      保存
    </wd-button> -->
    <wd-button block size="large" type="error" @click="handleReset">
      重置所有数据
    </wd-button>
    <view class="mt-16 text-center text-sm">
      <view class="flex items-center justify-center space-x-2">
        <text class="font-bold">
          友恒网络科技
        </text>
        <text class="text-gray">
          出品
        </text>
      </view>

      <view class="text-sm">
        sn：{{ sn }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
