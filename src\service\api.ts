import { http } from '@/utils/http'

export interface TaskListRes {
  success: boolean
  data: {
    taskId: string
    taskName: string
  }[]
}

export const getTaskListAPI = () => {
  return http.post<TaskListRes>('/api/getTaskList')
}

/* [
  [
    "资产名称1",
    "使用人1",
    "使用部门1",
    "规格型号1",
    "存放地点1",
    "条形码1"
  ],
  [
    "资产名称2",
    "使用人2",
    "使用部门2",
    "规格型号2",
    "存放地点2",
    "条形码2"
  ]
] */
export interface TaskDetailRes {
  success: boolean
  taskId: string
  taskName: string
  data: string[][]
}

export const getTaskDetailAPI = (taskId: string) => {
  return http.post<TaskDetailRes>('/api/getTask', { taskId })
}

export const TableTitle =[
  '资产名称',
  '规格型号',
  '条形码',
  '使用人',
  '存放地点',
  '使用部门',
  '是否清点',
  '是否更变资产名',
  '是否变更规格型号',
  '是否变更使用人',
  '是否变更存放地点',
  '是否变更使用部门',
]

export const setTitleAPI = () => {
  return http.post('/api/title', { title: TableTitle })
}

export interface SendDataToServerParams {
  taskId: string
  table: string[][]
}

export const sendDataToServerAPI = (data: SendDataToServerParams) => {
  return http.post<{ success: boolean }>('/api/table', data)
}