import type { AddressForm, EquipmentForm } from '@/store/dbStore'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const pointerStore = defineStore('pointerStore', () => {
  // 指针是内存指针，不能永久存储，要考虑应用开启时重新指向
  // 当前任务下的地址库指针
  const activeAddressList = ref<AddressForm[]>([])
  // 当下任务的所有设备指针
  const activeEquipmentList = ref<EquipmentForm[]>([])

  return {
    activeAddressList,
    activeEquipmentList,
  }
})
