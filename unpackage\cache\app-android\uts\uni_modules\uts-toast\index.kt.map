{"version": 3, "sources": ["uni_modules/uts-toast/utssdk/app-android/index.uts", "uni_modules/uts-toast/utssdk/interface.uts"], "sourcesContent": ["import { ToastOptions } from \"../interface\";\r\nimport Toast from 'android.widget.Toast';\r\n\r\nexport function showToast(option : ToastOptions) : void {\r\n\tclass MainThreadRunnable extends Runnable {\r\n\t\toverride run() {\r\n\t\t\tToast.makeText(UTSAndroid.getUniActivity()!, option.message, Toast.LENGTH_LONG).show()\r\n\t\t}\r\n\t}\r\n\tUTSAndroid.getUniActivity()?.runOnUiThread(new MainThreadRunnable())\r\n}", "export type ToastOptions = {\r\n\tmessage : string\r\n}\r\n\r\nexport type showToast = (option : ToastOptions) => void"], "names": [], "mappings": ";;AACA,OAAkB,oBAAsB,CAAC;;;;;;;;;;;;ACDd,WAAf;IACX;sBAAU,MAAM,CAAA;;;;;;ADEX,IAAU,UAAU,oBAAqB,GAAI,IAAI,CAAA;IACtD,WAAM,qBAA2B;;;;QAChC,aAAS,MAAG;YACX,MAAM,QAAQ,CAAC,WAAW,cAAc,MAAK,OAAO,OAAO,EAAE,MAAM,WAAW,EAAE,IAAI;QACrF;;IAED,WAAW,cAAc,IAAI,cAAc,AAAI;AAChD;ACV2B;IAC1B,kBAAA,SAAU,MAAM,CAAA;;kBDES,8BAAqB,GAAI,IAAI"}