<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '导出',
  },
}
</route>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { useMessage, useToast } from 'wot-design-uni'
import { dbHook } from '@/hooks/dbHook'
import { TableTitle } from '@/service/api'
import { pointerStore } from '@/store/pointerStore'
import { stepStore } from '@/store/stepStore'
import { getColor, rgbaToHex } from '@/utils/colorUtils'
import { toStepOne } from '@/utils/tool'

const rfidModule = uni.requireNativePlugin('rfidModule')

const toast = useToast()
const message = useMessage()
const { resetDb, combinedData, sendDataToServer } = dbHook()
const useStepStore = stepStore()
const { activeTaskName, activeTaskId } = storeToRefs(useStepStore)
const usePointerStore = pointerStore()
const { activeAddressList, activeEquipmentList } = storeToRefs(usePointerStore)

const taskPlanCount = ref(0)
const taskCompletedCount = ref(0)
const taskUnfinishedCount = ref(0)
const taskCompletedPercent = ref(0)
const successTip = ref('')

onShow(() => {
  getCountData()
  // 本页关闭RFID
  // rfidModule.toDisConnectDevice({}, (ret) => {})
})

function getCountData() {
  taskCompletedCount.value = 0
  taskUnfinishedCount.value = 0
  taskPlanCount.value = activeEquipmentList.value.length
  activeAddressList.value.forEach((item) => {
    taskCompletedCount.value += item.completedCodeArr.length
  })
  activeAddressList.value.forEach((item) => {
    taskUnfinishedCount.value += item.unfinishedCodeArr.length
  })
  if (taskPlanCount.value !== 0) {
    taskCompletedPercent.value = Math.floor(
      (taskCompletedCount.value / taskPlanCount.value) * 100,
    )
  }
  else {
    taskCompletedPercent.value = 0
  }
}

function handleReset() {
  message
    .confirm({
      msg: '请注意导出备份好清点数据',
      title: '是否清空所有数据？',
    })
    .then(() => {
      resetDb()
    })
}

function handleNetWorkOutput() {
  sendDataToServer(true)
  toast.success('导入网络任务成功')
}

function handleLocalOutput() {
  const data: string[][] = [TableTitle]
  combinedData(data)
  /*   // 已清点
  const isCompletedArr = []
  activeAddressList.value.forEach((item) => {
    isCompletedArr.push(...item.completedCodeArr)
  })

  activeEquipmentList.value.forEach((item) => {
    // 是否已清点
    const isCompleted = isCompletedArr.includes(item.code)
    data.push([
      item.name, // 资产名称
      item.modelType, // 规格型号
      '`' + item.code, // 条形码
      item.userName, // 使用人
      item.addressName, // 存放地点
      item.department, // 使用部门
      isCompleted ? '已清点' : '未清点', // 是否清点
      item.hadModify_name ? '变更' : '', // 是否更变资产名
      item.hadModify_modelType ? '变更' : '', // 是否变更规格型号
      item.hadModify_userName ? '变更' : '', // 是否变更使用人
      item.hadModify_address ? '变更' : '', // 是否变更存放地点
      item.hadModify_department ? '变更' : '', // 是否变更使用部门
    ])
  }) */

  const worksheet = 'sheet1'
  let str = ''

  // 循环遍历，每行加入tr标签，每个单元格加td标签
  for (let i = 0; i < data.length; i++) {
    str += '<tr>'
    for (let k = 0; k < data[i].length; k++) {
      // 增加\t为了不让表格显示科学计数法或者其他格式
      str += `<td>${`${data[i][k]}\t`}</td>`
    }
    str += '</tr>'
  }
  // 下载的表格模板数据
  const template = `<html xmlns:o="urn:schemas-microsoft-com:office:office"
        xmlns:x="urn:schemas-microsoft-com:office:excel"
        xmlns="http://www.w3.org/TR/REC-html40">
        <head><!--[if gte mso 9]><xml encoding="UTF-8"><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>
        <x:Name>${worksheet}</x:Name>
        <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet>
        </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
        </head><body><table>${str}</table></body></html>`

  exportFile(template)
  toast.success('导出本地统计表成功')
}

// 导出文件到手机 fileData:要写入到文件的数据，返回参数为文档路径
function exportFile(fileData: any, documentName = '资产清点导出') {
  /*
        PRIVATE_DOC: 应用私有文档目录常量
        PUBLIC_DOCUMENTS: 程序公用文档目录常量
        */
  // console.log(plus.io)

  plus.io.resolveLocalFileSystemURL('_downloads/', (entry) => {
    // const fullPath = rootObj.fullPath
    // const pathStr = fullPath.replace('/storage/emulated/0', '')
    const fileName = `清点统计表${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
    entry.getFile(
      fileName,
      { create: true },
      (dirEntry) => {
        dirEntry.createWriter(
          (writer) => {
            console.log('writer', writer)
            writer.onwritestart = () => {
              uni.showLoading({
                title: '正在导出',
                mask: true,
              })
            }

            writer.onwrite = function () {
              uni.hideLoading()
              successTip.value = `${entry.fullPath.replace('/storage/emulated/0', '')}${fileName}`
            }
            writer.onerror = function (e) {
              // console.error('写入文件失败：' + e.message)
            }
            // 写入内容
            writer.write(fileData)
          },
          (e) => {
            console.log(e.message)
          },
        )
      },
      (e) => {
        console.error(`获取文件失败：${e.message}`)
      },
    )
  })
}

function toSettingPage() {
  uni.navigateTo({
    url: '/pages/settingPage/index',
  })
}
</script>

<template>
  <wd-toast />
  <view class="p-4">
    <wd-button size="large" plain block type="info" @click="toSettingPage">
      前往配置
    </wd-button>
    <view class="p-4 text-xs">
      <view class="mb-2 mt-4 font-bold">
        注意事项：
      </view>
      <ul>
        <li>
          清点中额外发现的资产，只要判定（修改）了地址，那么将不会再出现在其他额外发现中。
        </li>
        <li>
          开始清点任务后，建议不要再到【新建】页面修改资产的地址，除非您明确你修改的资产是否受上一条注意事项所影响。
        </li>
      </ul>
    </view>
    <view v-if="activeTaskId === ''" class="f-no mt-12 center">
      <view class="o-btn bg-primary text-lg text-white" @click="toStepOne">
        请先新建或选择任务
      </view>
    </view>
    <view v-else>
      <view class="o-shadow mb-2 rd-2 bg-white p-4">
        <view class="mb-3 mt-2 text-2xl font-bold">
          任务统计情况
        </view>
        <view class="f-babel">
          <view class="f-label-name">
            任务名：
          </view>
          <view>{{ activeTaskName }}</view>
        </view>
        <view class="flex items-center justify-between gap-4">
          <view>
            <view class="f-babel">
              <view class="f-label-name">
                计划清点：
              </view>
              <view>{{ taskPlanCount }}</view>
            </view>
            <view class="f-babel">
              <view class="f-label-name">
                已清点数：
              </view>
              <view>{{ taskCompletedCount }}</view>
            </view>
            <view class="f-babel">
              <view class="f-label-name">
                未清点数：
              </view>
              <view>{{ taskUnfinishedCount }}</view>
            </view>
          </view>
          <wd-circle
            v-model="taskCompletedPercent"
            class="mr-4 shrink-0"
            :stroke-width="30"
            :color="rgbaToHex(getColor(taskCompletedPercent))"
            :text="`${taskCompletedPercent}%`"
          />
        </view>
      </view>
      <view class="mt-4 flex justify-between space-x-2">
        <view class="o-btn bg-primary flex-1 text-white" @click="handleLocalOutput">
          导出本地统计表
        </view>
        <view class="o-btn bg-primary flex-1 text-white" @click="handleNetWorkOutput">
          导出联网统计表
        </view>
      </view>

      <view v-if="successTip !== ''" class="mt-4">
        <view class="color-gray">
          输出路径：
        </view>
        <view>{{ successTip }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.f-babel {
  @apply flex mb-2 items-baseline;
}

.f-label-name {
  @apply shrink-0 color-gray;

  //width: 24vw;
}
</style>
