{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__904B921", "name": "unipluginDemo", "version": {"name": "1.0.0", "code": "100"}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Push": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"render": "always", "id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "nativePlugins": {"yunwei1-uniplugin-rfid": {"__plugin_info__": {"name": "unipluginRfid", "description": "yunwei1 rfid plugin", "platforms": "Android", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.36", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "launch_path": "__uniappview.html"}}