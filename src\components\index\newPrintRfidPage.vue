<script lang="ts" setup>
import type { EquipmentForm, OptionalEquipmentFormBase } from '@/store/dbStore'
import { storeToRefs } from 'pinia'
import { useMessage, useToast } from 'wot-design-uni'
import { dbHook } from '@/hooks/dbHook'
import { pointerStore } from '@/store/pointerStore'
import { stepStore } from '@/store/stepStore'
import { CreateStep } from '@/utils/enum'
import { delIdWithFindIndex, upDateWithFor } from '@/utils/tool'

const message = useMessage()
const toast = useToast()
const useStepStore = stepStore()
const { createStep, activeTaskId, activeAddressId } = storeToRefs(useStepStore)
const usePointerStore = pointerStore()
const { activeAddressList, activeEquipmentList } = storeToRefs(usePointerStore)

const { addressDataDb, equDataDb, delEqu, updateEqu, addAddress } = dbHook()

interface EquForm {
  id: string
  code: string
  name: string
  addressId: string
  addressName: string
  modelType: string
  userName: string
  department: string
}

const form = ref()
const formModel = reactive<EquForm>({
  id: '',
  code: '',
  name: '',
  addressId: '',
  addressName: '',
  modelType: '',
  userName: '',
  department: '',
})
// 用于检测是否有修改
const formModelCopy = {
  name: '',
  addressName: '',
  modelType: '',
  userName: '',
  department: '',
}
const isShowPicker = ref(false)
const showOverlay = ref(false)
const isShowEditor = ref(false)
const addressPickList = ref<
  {
    label: string
    value: string
    disabled?: boolean
  }[]
>([])

function getData() {
  // console.log('taskDataDb', taskDataDb.value)
  // console.log('addressDataDb', addressDataDb.value)
  // console.log('equDataDb', equDataDb.value)
  activeEquipmentList.value = []
  // 找出符合taskId的地址
  activeAddressList.value = addressDataDb.value.filter(
    item => item.taskId === activeTaskId.value,
  )
  addressPickList.value = activeAddressList.value.map(item => ({
    label: item.name,
    value: item.id,
  }))
  // 找出equDataDb中addressId===activeAddressList中存在的id
  activeEquipmentList.value = equDataDb.value.filter(item =>
    activeAddressList.value.some(
      addressItem => addressItem.id === item.addressId,
    ),
  )

  /*  equDataDb.value.forEach((equItem) => {
    if (
      activeAddressList.value.some(
        (addressItem) => addressItem.id === equItem.addressId,
      )
    ) {
      const addressName = activeAddressList.value.find(
        (item) => item.id === equItem.addressId,
      )?.name
      activeEquipmentList.value.push({ ...equItem, addressName })
    }
  }) */
}

onShow(() => {
  getData()
})

onHide(() => {
  // 切换底部tab时
  isShowEditor.value = false
})

function handleDelEqu(id: string, name: string) {
  message
    .confirm({
      msg: name,
      title: '是否删除资产',
    })
    .then(() => {
      delEqu(id)
      delIdWithFindIndex(activeEquipmentList.value, id)
    })
    .catch(() => {})
}
function handleSelectItem(id: string, hadPrintRfid: boolean) {
  showOverlay.value = true
  toast.loading('写入中...')
  setTimeout(() => {
    // 可以重新写入，未写入过的才需要更新标记
    if (!hadPrintRfid) {
      updateEqu(id, { hadPrintRfid: true }, false)
      upDateWithFor(id, activeEquipmentList.value, { hadPrintRfid: true })
    }
    toast.close()
    showOverlay.value = false
  }, 2000)
}

function toOtherTask() {
  createStep.value = CreateStep.createTask
  activeTaskId.value = ''
  activeAddressId.value = ''
}

function handleModifyItem(data: EquipmentForm) {
  // 缓存修改前数据
  formModelCopy.name = data.name
  formModelCopy.addressName = data.addressName
  formModelCopy.modelType = data.modelType
  formModelCopy.userName = data.userName
  formModelCopy.department = data.department
  formModel.id = data.id
  formModel.code = data.code
  formModel.name = data.name
  formModel.addressId = data.addressId
  formModel.addressName = data.addressName
  formModel.modelType = data.modelType
  formModel.userName = data.userName
  formModel.department = data.department
  // 开启编辑
  isShowEditor.value = true
}

function closeEditor() {
  isShowEditor.value = false
  isShowPicker.value = false
}

function handleSubmit() {
  form.value
    .validate()
    .then(({ valid }) => {
      if (valid) {
        // modifyData 要修改的各字段数据
        const modifyData: OptionalEquipmentFormBase = {}
        if (formModel.addressName !== formModelCopy.addressName) {
          modifyData.hadModify_address = true
          // 判断地址库是否有这个地址，如果有则获取id
          const addressItem = activeAddressList.value.find(
            item => item.name === formModel.addressName,
          )
          if (addressItem?.id) {
            // 有地址id
            modifyData.addressId = addressItem?.id
          }
          else {
            // 新建地址
            modifyData.addressId = addAddress(
              activeTaskId.value,
              formModel.addressName,
              formModel.code,
            )
          }
          modifyData.addressName = formModel.addressName
        }
        if (formModel.name !== formModelCopy.name) {
          modifyData.name = formModel.name
          modifyData.hadModify_name = true
        }
        if (formModel.modelType !== formModelCopy.modelType) {
          modifyData.modelType = formModel.modelType
          modifyData.hadModify_modelType = true
        }
        if (formModel.userName !== formModelCopy.userName) {
          modifyData.userName = formModel.userName
          modifyData.hadModify_userName = true
        }
        if (formModel.department !== formModelCopy.department) {
          modifyData.department = formModel.department
          modifyData.hadModify_department = true
        }
        if (updateEqu(formModel.id, modifyData, modifyData.hadModify_address)) {
          toast.success('修改成功')
          isShowEditor.value = false
          isShowPicker.value = false
          getData()
        }
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

function pickChange() {
  formModel.addressName = addressPickList.value.find(
    item => item.value === formModel.addressId,
  ).label
}

function toScanAndWrite() {
  uni.navigateTo({
    url: '/pages/scanAndWrite/index',
  })
}
</script>

<template>
  <view>
    <wd-overlay :show="showOverlay" />
    <wd-toast />
    <wd-message-box />
    <view class="o-shadow fixed left-0 top-0 z-1 w-full bg-white">
      <view class="bg-primary shrink-0 pb-2 pt-10 text-center text-white font-bold">
        资产清单准备
      </view>
      <view class="flex gap-2 p-4">
        <wd-button class="grow-1" plain type="info" @click="toOtherTask">
          选择其他任务
        </wd-button>
        <wd-button
          class="grow-2"
          custom-class="custom-shadow"
          @click="toScanAndWrite"
        >
          扫码写入
        </wd-button>
      </view>
    </view>
    <view class="px-3 pb-20 pt-38">
      <view
        v-for="item in activeEquipmentList"
        :key="item.id"
        class="o-shadow mb-2 rd-2 bg-white"
      >
        <view class="p-4">
          <view class="mt-2 color-gray">
            {{ item.code }}
          </view>
          <view class="flex items-center gap-2 py-2">
            <wd-icon name="discount" size="22px" />
            <view class="py-2 text-xl font-bold">
              {{ item.name }}
            </view>
          </view>
          <view class="f-babel">
            <view class="f-label-name">
              规格型号：
            </view>
            <view>{{ item.modelType }}</view>
          </view>
          <view class="f-babel">
            <view class="f-label-name">
              使用人：
            </view>
            <view>{{ item.userName }}</view>
          </view>
          <view class="f-babel">
            <view class="f-label-name">
              使用部门：
            </view>
            <view>{{ item.department }}</view>
          </view>
          <view class="f-babel">
            <view class="f-label-name">
              存放地点：
            </view>
            <view>{{ item.addressName }}</view>
          </view>
        </view>
        <view class="mt-1 flex items-center justify-between px-4 pb-4">
          <!--          <view
            class="o-btn mr-2 o-border color-red"
            @click="handleDelEqu(item.id, item.name)"
          >
            删除
          </view> -->
          <view
            class="o-btn o-border text-primary mr-2"
            @click="handleModifyItem(item)"
          >
            修改
          </view>
          <!--          <view
            :class="item.hadPrintRfid ? 'bg-yellow' : 'bg-primary'"
            class="o-btn text-white grow-1"
            @click="handleSelectItem(item.id, item.hadPrintRfid)"
          >
            {{ item.hadPrintRfid ? '已写入' : '写入RFID' }}
          </view> -->
        </view>
      </view>
    </view>
    <wd-action-sheet
      v-model="isShowEditor"
      :title="formModel.code"
      @close="closeEditor"
    >
      <view class="flex flex-col justify-between" style="height: 86vh">
        <wd-form ref="form" :model="formModel">
          <wd-cell-group border>
            <wd-input
              v-model="formModel.name"
              :rules="[{ required: true, message: '请填写资源名称' }]"
              clearable
              label="资源名称"
              label-width="80px"
              prop="name"
            />
            <wd-input
              v-model="formModel.modelType"
              :rules="[{ required: true, message: '请填写规格型号' }]"
              clearable
              label="规格型号"
              label-width="80px"
              prop="modelType"
            />
            <wd-input
              v-model="formModel.userName"
              :rules="[{ required: true, message: '请填写使用人' }]"
              clearable
              label="使用人"
              label-width="80px"
              prop="userName"
            />
            <wd-input
              v-model="formModel.department"
              :rules="[{ required: true, message: '请填写使用部门' }]"
              clearable
              label="使用部门"
              label-width="80px"
              prop="department"
            />
            <view class="flex items-center">
              <wd-input
                v-model="formModel.addressName"
                :rules="[{ required: true, message: '请填写存放地点' }]"
                clearable
                label="存放地点"
                label-width="80px"
                prop="addressName"
              />
              <view
                class="o-btn o-border mr-2 shrink-0"
                type="primary"
                @click="isShowPicker = !isShowPicker"
              >
                选择
              </view>
            </view>
            <wd-picker-view
              v-if="isShowPicker"
              v-model="formModel.addressId"
              :columns="addressPickList"
              :columns-height="180"
              @change="pickChange"
            />
          </wd-cell-group>
        </wd-form>
        <view class="px-3 pb-3">
          <wd-button block size="large" type="primary" @click="handleSubmit">
            保存
          </wd-button>
        </view>
      </view>
    </wd-action-sheet>
  </view>
</template>

<style lang="scss" scoped>
.f-babel {
  @apply flex mb-1 items-baseline;
}

.f-label-name {
  @apply shrink-0 color-gray;

  width: 24vw;
}
</style>
