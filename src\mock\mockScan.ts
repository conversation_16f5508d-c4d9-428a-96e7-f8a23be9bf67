const outputList = [
  '040446546546654',
  '040446546546655',
  '040446546546656',
  '040446546546657',
  '040446546546658',
  '040446546546659',
  '040446546546660',
  '040446546546661',
  '040446546546662',
  '040446546546663',
  '040446546546664',
  '040446546546665',
  '040446546546666',
  '040446546546667',
  '040446546546668',
  '040446546546669',
  '040446546546670',
  '040446546546671',
  '040446546546672',
  '040446546546673',
  '040446546546674',
  '040446546546675',
  '040446546546676',
  '040446546546677',
  '040446546546678',
]

// 调用一次函数，从outputList随机抽出一个数
export const getRandomNumber = () => {
  const randomIndex = Math.floor(Math.random() * outputList.length)
  return outputList[randomIndex]
}
