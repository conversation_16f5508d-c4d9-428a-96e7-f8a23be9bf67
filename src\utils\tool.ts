export const delIdWithFindIndex = (scoreData: any[], id: string) => {
  const index = scoreData.findIndex((item) => item.id === id)
  if (index !== -1) {
    // 使用 splice 删除元素
    scoreData.splice(index, 1)
  } else {
    uni.showToast({
      title: '未找到id:' + id,
      duration: 2000,
    })
  }
}

interface Identifiable {
  id: string
}

/**
 * 更新数据
 * @param id
 * @param scoreData 数据源数组
 * @param data 更新的内容
 */
export const upDateWithFor = <T extends Identifiable, K>(
  id: string,
  scoreData: T[],
  data: K,
) => {
  for (let i = 0; i < scoreData.length; i++) {
    if (scoreData[i].id === id) {
      scoreData[i] = {
        ...scoreData[i],
        ...data,
      }
      return true
    }
  }

  return false
}

export const toStepOne = () => {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
