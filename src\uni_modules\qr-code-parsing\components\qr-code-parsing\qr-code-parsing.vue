<template>
  <view class="qr-code-parsing">
    <slot v-if="isCustom"></slot>
    <view class="box" v-else>
      <image class="qrcode" :src="awm" mode="aspectFit" @click="camera" />
      <button class="btn" @click="deCode()">解码</button>
    </view>
  </view>
</template>

<script>
import QrCode from './qrcode.js'

export default {
  name: 'QrCodeParsing',
  props: {
    isCustom: {
      type: Boolean,
      default: false,
    },
    awmFilePath: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      awm: 'data:image/png;base64,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',
    }
  },
  created() {
    if (this.awmFilePath && !this.isCustom) {
      this.awm = this.awmFilePath
    }
  },
  methods: {
    camera() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.awm = res.tempFilePaths[0]
        },
      })
    },
    deCode(value) {
      const awm = value || this.awm
      QrCode.decode(awm)
      QrCode.callback = (result) => {
        this.$emit('callback', result)
      }
    },
  },
}
</script>

<style>
.qr-code-parsing .qrcode {
  padding: 10px;
  border: 1px solid #ccc;
}
.qr-code-parsing .btn {
  margin-top: 10px;
}
</style>
